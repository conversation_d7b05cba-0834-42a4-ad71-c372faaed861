import logging
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
from typing import Dict, List

logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Comprehensive options strategy types for Indian market"""

    # Basic Directional Strategies
    LONG_CALL = "long_call"
    LONG_PUT = "long_put"
    PROTECTIVE_PUT = "protective_put"

    # ATM Strategies
    ATM_LONG_CALL = "atm_long_call"
    ATM_LONG_PUT = "atm_long_put"

    # OTM Strategies
    OTM_LONG_CALL = "otm_long_call"
    OTM_LONG_PUT = "otm_long_put"

    # Far OTM Strategies (Deep OTM)
    FAR_OTM_LONG_CALL = "far_otm_long_call"
    FAR_OTM_LONG_PUT = "far_otm_long_put"
    
    # Deep OTM Strategies (Very Far OTM)
    DEEP_OTM_LONG_CALL = "deep_otm_long_call"
    DEEP_OTM_LONG_PUT = "deep_otm_long_put"
    
    # Intraday Strategies
    INTRADAY_SCALPING_CALL = "intraday_scalping_call"
    INTRADAY_SCALPING_PUT = "intraday_scalping_put"
    INTRADAY_MOMENTUM_CALL = "intraday_momentum_call"
    INTRADAY_MOMENTUM_PUT = "intraday_momentum_put"
    INTRADAY_REVERSAL_CALL = "intraday_reversal_call"
    INTRADAY_REVERSAL_PUT = "intraday_reversal_put"
    
    # Gamma Scalping Strategies
    GAMMA_SCALPING_LONG = "gamma_scalping_long"
    DELTA_NEUTRAL_GAMMA_SCALP = "delta_neutral_gamma_scalp"
    
    # Volatility Breakout Strategies
    VOLATILITY_BREAKOUT_LONG = "volatility_breakout_long"
    VIX_BASED_STRATEGY = "vix_based_strategy"

    # Volatility Strategies
    LONG_STRADDLE = "long_straddle"
    LONG_STRANGLE = "long_strangle"
    REVERSE_IRON_CONDOR = "reverse_iron_condor" # Net debit strategy
    REVERSE_IRON_BUTTERFLY = "reverse_iron_butterfly" # Net debit strategy

    # Spread Strategies (Net Debit Spreads)
    BULL_CALL_SPREAD = "bull_call_spread"
    BEAR_PUT_SPREAD = "bear_put_spread"
    CALL_CALENDAR_SPREAD = "call_calendar_spread"
    PUT_CALENDAR_SPREAD = "put_calendar_spread"
    DIAGONAL_CALL_SPREAD = "diagonal_call_spread" # Can be debit or credit, but often debit
    DIAGONAL_PUT_SPREAD = "diagonal_put_spread"  # Can be debit or credit, but often debit

    # Ratio Strategies (Backspreads are net debit)
    RATIO_CALL_BACKSPREAD = "ratio_call_backspread"
    RATIO_PUT_BACKSPREAD = "ratio_put_backspread"

    # Collar Strategies
    COLLAR = "collar"

    # Synthetic Strategies
    SYNTHETIC_LONG = "synthetic_long"
    SYNTHETIC_CALL = "synthetic_call" # Synthetic long call
    SYNTHETIC_PUT = "synthetic_put"  # Synthetic long put

    # Complex Multi-leg Strategies (Often net debit)
    BUTTERFLY_SPREAD = "butterfly_spread" # Can be debit or credit, but often debit
    CONDOR_SPREAD = "condor_spread" # Can be debit or credit, but often debit
    CHRISTMAS_TREE = "christmas_tree" # Often a debit strategy

    # Indian Market Specific Buying Strategies
    WEEKLY_EXPIRY_STRADDLE = "weekly_expiry_straddle"
    MONTHLY_EXPIRY_STRANGLE = "monthly_expiry_strangle"
    BANKNIFTY_BUTTERFLY = "banknifty_butterfly" # Assuming this is a buying butterfly

@dataclass
class OptionsLeg:
    """Single options leg in a strategy"""
    symbol: str
    option_type: str  # CE or PE
    strike_price: float
    quantity: int  # Positive for long, negative for short
    premium: float
    underlying: str

@dataclass
class OptionsStrategy:
    """Complete options strategy definition"""
    strategy_id: str
    strategy_type: StrategyType
    underlying: str
    legs: List[OptionsLeg]
    max_profit: float
    max_loss: float
    break_even_points: List[float]
    probability_of_profit: float
    net_premium: float
    margin_required: float
    risk_reward_ratio: float
    target_profit: float
    stop_loss: float
    created_at: datetime
    entry_conditions: List[Dict]
    exit_conditions: List[Dict]
    
    def to_dict(self) -> Dict:
        """Convert strategy to dictionary"""
        return {
            'strategy_id': self.strategy_id,
            'strategy_type': self.strategy_type.value,
            'underlying': self.underlying,
            'legs': [asdict(leg) for leg in self.legs],
            'max_profit': self.max_profit,
            'max_loss': self.max_loss,
            'break_even_points': self.break_even_points,
            'probability_of_profit': self.probability_of_profit,
            'net_premium': self.net_premium,
            'margin_required': self.margin_required,
            'risk_reward_ratio': self.risk_reward_ratio,
            'target_profit': self.target_profit,
            'stop_loss': self.stop_loss,
            'created_at': self.created_at.isoformat(),
            'entry_conditions': self.entry_conditions,
            'exit_conditions': self.exit_conditions
        }
