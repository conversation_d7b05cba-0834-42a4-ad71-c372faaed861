import logging
import polars as pl
from datetime import datetime
from typing import Dict, List, Any
from agents.strategy_generation.data_models import OptionsLeg, OptionsStrategy, StrategyType
from agents.strategy_generation.strategy_calculators import StrategyCalculators

logger = logging.getLogger(__name__)

class StrategyGenerators:
    """Generates various options strategies."""

    def __init__(self, config: Dict, calculators: StrategyCalculators):
        self.config = config
        self.calculators = calculators

    async def generate_strategy_type(self, strategy_type: StrategyType, underlying: str,
                                    option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate strategies of specific type."""
        try:
            strategies = []

            # Basic directional buying strategies
            if strategy_type == StrategyType.LONG_CALL:
                strategies = await self._generate_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.LONG_PUT:
                strategies = await self._generate_long_put_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.PROTECTIVE_PUT:
                strategies = await self._generate_protective_put_strategies(underlying, option_chain, spot_price)

            # ATM buying strategies
            elif strategy_type == StrategyType.ATM_LONG_CALL:
                strategies = await self._generate_atm_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.ATM_LONG_PUT:
                strategies = await self._generate_atm_long_put_strategies(underlying, option_chain, spot_price)

            # OTM buying strategies
            elif strategy_type == StrategyType.OTM_LONG_CALL:
                strategies = await self._generate_otm_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.OTM_LONG_PUT:
                strategies = await self._generate_otm_long_put_strategies(underlying, option_chain, spot_price)

            # Far OTM buying strategies
            elif strategy_type == StrategyType.FAR_OTM_LONG_CALL:
                strategies = await self._generate_far_otm_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.FAR_OTM_LONG_PUT:
                strategies = await self._generate_far_otm_long_put_strategies(underlying, option_chain, spot_price)

            # Intraday buying strategies
            elif strategy_type == StrategyType.INTRADAY_SCALPING_CALL:
                strategies = await self._generate_intraday_scalping_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.INTRADAY_SCALPING_PUT:
                strategies = await self._generate_intraday_scalping_put_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.INTRADAY_MOMENTUM_CALL:
                strategies = await self._generate_intraday_scalping_call_strategies(underlying, option_chain, spot_price)  # Use scalping as fallback
            elif strategy_type == StrategyType.INTRADAY_MOMENTUM_PUT:
                strategies = await self._generate_intraday_scalping_put_strategies(underlying, option_chain, spot_price)  # Use scalping as fallback
            elif strategy_type == StrategyType.INTRADAY_REVERSAL_CALL:
                strategies = await self._generate_intraday_scalping_call_strategies(underlying, option_chain, spot_price)  # Use scalping as fallback
            elif strategy_type == StrategyType.INTRADAY_REVERSAL_PUT:
                strategies = await self._generate_intraday_scalping_put_strategies(underlying, option_chain, spot_price)  # Use scalping as fallback

            # Gamma Scalping (Long)
            elif strategy_type == StrategyType.GAMMA_SCALPING_LONG:
                strategies = await self._generate_gamma_scalping_long_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.DELTA_NEUTRAL_GAMMA_SCALP:
                strategies = await self._generate_delta_neutral_gamma_scalp_strategies(underlying, option_chain, spot_price)

            # Volatility Breakout (Long)
            elif strategy_type == StrategyType.VOLATILITY_BREAKOUT_LONG:
                strategies = await self._generate_volatility_breakout_long_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.VIX_BASED_STRATEGY:
                strategies = await self._generate_vix_based_strategy_strategies(underlying, option_chain, spot_price)

            # Volatility buying strategies
            elif strategy_type == StrategyType.LONG_STRADDLE:
                strategies = await self._generate_long_straddle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.LONG_STRANGLE:
                strategies = await self._generate_long_strangle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.REVERSE_IRON_CONDOR:
                strategies = await self._generate_reverse_iron_condor_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.REVERSE_IRON_BUTTERFLY:
                strategies = await self._generate_reverse_iron_butterfly_strategies(underlying, option_chain, spot_price)

            # Spread buying strategies (net debit spreads)
            elif strategy_type == StrategyType.BULL_CALL_SPREAD:
                strategies = await self._generate_bull_call_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.BEAR_PUT_SPREAD:
                strategies = await self._generate_bear_put_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.CALL_CALENDAR_SPREAD:
                strategies = await self._generate_call_calendar_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.PUT_CALENDAR_SPREAD:
                strategies = await self._generate_put_calendar_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.DIAGONAL_CALL_SPREAD:
                strategies = await self._generate_diagonal_call_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.DIAGONAL_PUT_SPREAD:
                strategies = await self._generate_diagonal_put_spread_strategies(underlying, option_chain, spot_price)

            # Ratio buying strategies (backspreads are net debit)
            elif strategy_type == StrategyType.RATIO_CALL_BACKSPREAD:
                strategies = await self._generate_ratio_call_backspread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.RATIO_PUT_BACKSPREAD:
                strategies = await self._generate_ratio_put_backspread_strategies(underlying, option_chain, spot_price)

            # Collar strategies (often net zero or small debit/credit)
            elif strategy_type == StrategyType.COLLAR:
                strategies = await self._generate_collar_strategies(underlying, option_chain, spot_price)

            # Synthetic buying strategies
            elif strategy_type == StrategyType.SYNTHETIC_LONG:
                strategies = await self._generate_synthetic_long_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.SYNTHETIC_CALL:
                strategies = await self._generate_synthetic_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.SYNTHETIC_PUT:
                strategies = await self._generate_synthetic_put_strategies(underlying, option_chain, spot_price)

            # Complex Multi-leg buying strategies
            elif strategy_type == StrategyType.BUTTERFLY_SPREAD:
                strategies = await self._generate_butterfly_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.CONDOR_SPREAD:
                strategies = await self._generate_condor_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.CHRISTMAS_TREE:
                strategies = await self._generate_christmas_tree_strategies(underlying, option_chain, spot_price)

            # Indian market specific buying strategies
            elif strategy_type == StrategyType.WEEKLY_EXPIRY_STRADDLE:
                strategies = await self._generate_weekly_expiry_straddle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.MONTHLY_EXPIRY_STRANGLE:
                strategies = await self._generate_monthly_expiry_strangle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.BANKNIFTY_BUTTERFLY:
                strategies = await self._generate_banknifty_butterfly_strategies(underlying, option_chain, spot_price)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate {strategy_type.value} strategies: {e}")
            return []
    
    async def _generate_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, 
                                           spot_price: float) -> List[OptionsStrategy]:
        """Generate long call strategies"""
        try:
            strategies = []
            
            # Get call options
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            
            for call_row in calls.iter_rows(named=True):
                # Create single leg strategy
                leg = OptionsLeg(
                    symbol=call_row['symbol'],
                    option_type='CE',
                    strike_price=call_row['strike_price'],
                    expiry_date=call_row.get('expiry_date', '2024-01-25'),  # Use actual expiry or fallback
                    quantity=1,
                    premium=call_row['ltp'],
                    underlying=underlying
                )
                
                # Calculate strategy metrics
                max_profit = float('inf')  # Unlimited upside
                max_loss = call_row['ltp']
                break_even = call_row['strike_price'] + call_row['ltp']
                
                # Estimate probability of profit (simplified)
                prob_profit = max(0.0, min(1.0, (spot_price - break_even) / spot_price + 0.5))
                
                strategy = OptionsStrategy(
                    strategy_id=f"LC_{underlying}_{call_row['strike_price']}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call_row['ltp'],  # Negative because we pay premium
                    margin_required=call_row['ltp'],
                    risk_reward_ratio=float('inf') if max_loss > 0 else 0,
                    target_profit=max_loss * 2,  # 200% return target
                    stop_loss=max_loss * 0.5,  # 50% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Price", "operator": "break_above", "value": spot_price * 1.01}],
                    exit_conditions=[{"indicator": "Price", "operator": "fall_below", "value": spot_price * 0.99}]
                )
                
                strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long call strategies: {e}")
            return []
    
    async def _generate_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, 
                                          spot_price: float) -> List[OptionsStrategy]:
        """Generate long put strategies"""
        try:
            strategies = []
            
            # Get put options
            puts = option_chain.filter(pl.col('option_type') == 'PE')
            
            for put_row in puts.iter_rows(named=True):
                # Create single leg strategy
                leg = OptionsLeg(
                    symbol=put_row['symbol'],
                    option_type='PE',
                    strike_price=put_row['strike_price'],
                    expiry_date=put_row.get('expiry_date', '2024-01-25'),  # Use actual expiry or fallback
                    quantity=1,
                    premium=put_row['ltp'],
                    underlying=underlying
                )
                
                # Calculate strategy metrics
                max_profit = put_row['strike_price'] - put_row['ltp']
                max_loss = put_row['ltp']
                break_even = put_row['strike_price'] - put_row['ltp']
                
                # Estimate probability of profit
                prob_profit = max(0.0, min(1.0, (break_even - spot_price) / spot_price + 0.5))
                
                strategy = OptionsStrategy(
                    strategy_id=f"LP_{underlying}_{put_row['strike_price']}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_row['ltp'],
                    margin_required=put_row['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "RSI", "operator": "<", "value": 30}], # Placeholder
                    exit_conditions=[{"indicator": "RSI", "operator": ">", "value": 70}] # Placeholder
                )
                
                strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long put strategies: {e}")
            return []
    
    async def _generate_long_straddle_strategies(self, underlying: str, option_chain: pl.DataFrame, 
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate long straddle strategies"""
        try:
            strategies = []
            
            # Get unique strikes
            strikes = option_chain['strike_price'].unique().sort().to_list()
            
            for strike in strikes:
                # Get call and put at same strike
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') & 
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') & 
                    (pl.col('strike_price') == strike)
                ).height > 0 else None
                
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') & 
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') & 
                    (pl.col('strike_price') == strike)
                ).height > 0 else None
                
                if not call or not put:
                    continue
                
                # Create legs
                call_leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call.get('expiry_date', '2024-01-25'),
                    quantity=1,
                    premium=call['ltp'],
                    underlying=underlying
                )

                put_leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put.get('expiry_date', '2024-01-25'),
                    quantity=1,
                    premium=put['ltp'],
                    underlying=underlying
                )
                
                # Calculate strategy metrics
                total_premium = call['ltp'] + put['ltp']
                max_profit = float('inf')  # Unlimited if big move
                max_loss = total_premium
                break_even_upper = strike + total_premium
                break_even_lower = strike - total_premium
                
                # Estimate probability of profit (move beyond break-evens)
                prob_profit = 0.4  # Simplified estimate for volatility strategy
                
                strategy = OptionsStrategy(
                    strategy_id=f"LS_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.LONG_STRADDLE,
                    underlying=underlying,
                    legs=[call_leg, put_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=-total_premium,
                    margin_required=total_premium,
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "IV", "operator": "low"}], # Placeholder
                    exit_conditions=[{"indicator": "IV", "operator": "high"}] # Placeholder
                )
                
                strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long straddle strategies: {e}")
            return []
    
    async def _generate_long_strangle_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate long strangle strategies"""
        try:
            strategies = []

            # Get OTM strikes for calls and puts
            otm_call_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'CE')
            otm_put_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'PE')

            for call_strike in otm_call_strikes[:3]:  # Limit to top 3
                for put_strike in otm_put_strikes[:3]:
                    if call_strike <= put_strike:
                        continue

                    # Get call and put options
                    call = option_chain.filter(
                        (pl.col('option_type') == 'CE') &
                        (pl.col('strike_price') == call_strike)
                    ).row(0, named=True) if option_chain.filter(
                        (pl.col('option_type') == 'CE') &
                        (pl.col('strike_price') == call_strike)
                    ).height > 0 else None

                    put = option_chain.filter(
                        (pl.col('option_type') == 'PE') &
                        (pl.col('strike_price') == put_strike)
                    ).row(0, named=True) if option_chain.filter(
                        (pl.col('option_type') == 'PE') &
                        (pl.col('strike_price') == put_strike)
                    ).height > 0 else None

                    if not call or not put:
                        continue

                    # Create legs
                    call_leg = OptionsLeg(
                        symbol=call['symbol'],
                        option_type='CE',
                        strike_price=call_strike,
                        expiry_date=call.get('expiry_date', '2024-01-25'),
                        quantity=1,
                        premium=call['ltp'],
                        underlying=underlying
                    )

                    put_leg = OptionsLeg(
                        symbol=put['symbol'],
                        option_type='PE',
                        strike_price=put_strike,
                        expiry_date=put.get('expiry_date', '2024-01-25'),
                        quantity=1,
                        premium=put['ltp'],
                        underlying=underlying
                    )

                    # Calculate strategy metrics
                    total_premium = call['ltp'] + put['ltp']
                    max_profit = float('inf')  # Unlimited if big move
                    max_loss = total_premium
                    break_even_upper = call_strike + total_premium
                    break_even_lower = put_strike - total_premium

                    # Estimate probability of profit
                    prob_profit = 0.35  # Lower than straddle due to wider strikes

                    strategy = OptionsStrategy(
                        strategy_id=f"LST_{underlying}_{call_strike}_{put_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                        strategy_type=StrategyType.LONG_STRANGLE,
                        underlying=underlying,
                        legs=[call_leg, put_leg],
                        max_profit=max_profit,
                        max_loss=max_loss,
                        break_even_points=[break_even_lower, break_even_upper],
                        probability_of_profit=prob_profit,
                        net_premium=-total_premium,
                        margin_required=total_premium,
                        risk_reward_ratio=float('inf'),
                        target_profit=max_loss * 2,
                        stop_loss=max_loss * 0.5,
                        created_at=datetime.now(),
                        entry_conditions=[{"indicator": "IV", "operator": "low"}], # Placeholder
                        exit_conditions=[{"indicator": "IV", "operator": "high"}] # Placeholder
                    )

                    strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long strangle strategies: {e}")
            return []
    
    # ATM Strategy Methods
    async def _generate_atm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM long call strategies"""
        try:
            strategies = []
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not call:
                    continue

                leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call.get('expiry_date', '2024-01-25'),
                    quantity=1,
                    premium=call['ltp'],
                    underlying=underlying
                )

                max_profit = float('inf')
                max_loss = call['ltp']
                break_even = strike + call['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMLC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call['ltp'],
                    margin_required=call['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": ">", "value": 10000}], # Placeholder
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM long call strategies: {e}")
            return []

    async def _generate_atm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                              spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM long put strategies"""
        try:
            strategies = []
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not put:
                    continue

                leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put.get('expiry_date', '2024-01-25'),
                    quantity=1,
                    premium=put['ltp'],
                    underlying=underlying
                )

                max_profit = strike - put['ltp']
                max_loss = put['ltp']
                break_even = strike - put['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMLP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put['ltp'],
                    margin_required=put['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": ">", "value": 10000}], # Placeholder
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM long put strategies: {e}")
            return []

    async def _generate_atm_short_call_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM short call strategies"""
        try:
            strategies = []
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not call:
                    continue

                leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call.get('expiry_date', '2024-01-25'),
                    quantity=-1,  # Short position
                    premium=call['ltp'],
                    underlying=underlying
                )

                max_profit = call['ltp']
                max_loss = float('inf')  # Unlimited loss
                break_even = strike + call['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMSC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_SHORT_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=call['ltp'],  # Positive because we receive premium
                    margin_required=call['ltp'] * 10,  # Estimated margin
                    risk_reward_ratio=0,  # Unlimited loss
                    target_profit=max_profit * 0.8,  # 80% of max profit
                    stop_loss=max_profit * 2,  # 200% of premium received
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Delta", "operator": "near_zero"}], # Placeholder
                    exit_conditions=[{"indicator": "Delta", "operator": "far_from_zero"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM short call strategies: {e}")
            return []

    async def _generate_atm_short_put_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM short put strategies"""
        try:
            strategies = []
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not put:
                    continue

                leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put.get('expiry_date', '2024-01-25'),
                    quantity=-1,  # Short position
                    premium=put['ltp'],
                    underlying=underlying
                )

                max_profit = put['ltp']
                max_loss = strike - put['ltp']
                break_even = strike - put['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMSP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_SHORT_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=put['ltp'],  # Positive because we receive premium
                    margin_required=put['ltp'] * 10,  # Estimated margin
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_profit * 0.8,  # 80% of max profit
                    stop_loss=max_profit * 2,  # 200% of premium received
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Delta", "operator": "near_zero"}], # Placeholder
                    exit_conditions=[{"indicator": "Delta", "operator": "far_from_zero"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM short put strategies: {e}")
            return []

    async def _generate_bull_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                  spot_price: float) -> List[OptionsStrategy]:
        """Generate bull call spread strategies (net debit)"""
        try:
            strategies = []
            calls = option_chain.filter(pl.col('option_type') == 'CE').sort('strike_price')

            # Group calls by expiry date
            for expiry_date in calls['expiry_date'].unique().to_list():
                expiry_calls = calls.filter(pl.col('expiry_date') == expiry_date)

                if expiry_calls.height < 2:
                    continue

                # Iterate through possible combinations for long and short calls
                for i in range(expiry_calls.height):
                    long_call_row = expiry_calls.row(i, named=True)
                    for j in range(i + 1, expiry_calls.height):
                        short_call_row = expiry_calls.row(j, named=True)

                        # Ensure long call strike is lower than short call strike
                        if long_call_row['strike_price'] >= short_call_row['strike_price']:
                            continue

                        # Create legs
                        long_leg = OptionsLeg(
                            symbol=long_call_row['symbol'],
                            option_type='CE',
                            strike_price=long_call_row['strike_price'],
                            expiry_date=expiry_date,
                            quantity=1,
                            premium=long_call_row['ltp'],
                            underlying=underlying
                        )

                        short_leg = OptionsLeg(
                            symbol=short_call_row['symbol'],
                            option_type='CE',
                            strike_price=short_call_row['strike_price'],
                            expiry_date=expiry_date,
                            quantity=-1,  # Short position
                            premium=short_call_row['ltp'],
                            underlying=underlying
                        )

                        # Calculate strategy metrics
                        net_premium = long_call_row['ltp'] - short_call_row['ltp']
                        if net_premium <= 0:  # Must be a debit spread
                            continue

                        max_profit = (short_call_row['strike_price'] - long_call_row['strike_price']) - net_premium
                        max_loss = net_premium
                        break_even = long_call_row['strike_price'] + net_premium

                        # Estimate probability of profit (simplified)
                        prob_profit = max(0.0, min(1.0, (spot_price - break_even) / (short_call_row['strike_price'] - long_call_row['strike_price']) + 0.5))

                        strategy = OptionsStrategy(
                            strategy_id=f"BCS_{underlying}_{long_call_row['strike_price']}_{short_call_row['strike_price']}_{expiry_date}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                            strategy_type=StrategyType.BULL_CALL_SPREAD,
                            underlying=underlying,
                            legs=[long_leg, short_leg],
                            max_profit=max_profit,
                            max_loss=max_loss,
                            break_even_points=[break_even],
                            probability_of_profit=prob_profit,
                            net_premium=-net_premium,  # Negative for debit
                            margin_required=max_loss,  # Margin is max loss for debit spreads
                            risk_reward_ratio=max_profit / max_loss if max_loss > 0 else float('inf'),
                            target_profit=max_profit * 0.8,
                            stop_loss=max_loss * 0.5,
                            created_at=datetime.now(),
                            entry_conditions=[{"indicator": "Price", "operator": "above_support", "value": spot_price * 0.98}],
                            exit_conditions=[{"indicator": "Price", "operator": "below_resistance", "value": spot_price * 1.05}]
                        )
                        strategies.append(strategy)
            return strategies
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate bull call spread strategies: {e}")
            return []
    
    async def _generate_bear_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                 spot_price: float) -> List[OptionsStrategy]:
        """Generate bear put spread strategies (net debit)"""
        try:
            strategies = []
            puts = option_chain.filter(pl.col('option_type') == 'PE').sort('strike_price', descending=True)

            # Group puts by expiry date
            for expiry_date in puts['expiry_date'].unique().to_list():
                expiry_puts = puts.filter(pl.col('expiry_date') == expiry_date)

                if expiry_puts.height < 2:
                    continue

                # Iterate through possible combinations for long and short puts
                for i in range(expiry_puts.height):
                    long_put_row = expiry_puts.row(i, named=True)
                    for j in range(i + 1, expiry_puts.height):
                        short_put_row = expiry_puts.row(j, named=True)

                        # Ensure long put strike is higher than short put strike
                        if long_put_row['strike_price'] <= short_put_row['strike_price']:
                            continue

                        # Create legs
                        long_leg = OptionsLeg(
                            symbol=long_put_row['symbol'],
                            option_type='PE',
                            strike_price=long_put_row['strike_price'],
                            expiry_date=expiry_date,
                            quantity=1,
                            premium=long_put_row['ltp'],
                            underlying=underlying
                        )

                        short_leg = OptionsLeg(
                            symbol=short_put_row['symbol'],
                            option_type='PE',
                            strike_price=short_put_row['strike_price'],
                            expiry_date=expiry_date,
                            quantity=-1,  # Short position
                            premium=short_put_row['ltp'],
                            underlying=underlying
                        )

                        # Calculate strategy metrics
                        net_premium = long_put_row['ltp'] - short_put_row['ltp']
                        if net_premium <= 0:  # Must be a debit spread
                            continue

                        max_profit = (long_put_row['strike_price'] - short_put_row['strike_price']) - net_premium
                        max_loss = net_premium
                        break_even = long_put_row['strike_price'] - net_premium

                        # Estimate probability of profit (simplified)
                        prob_profit = max(0.0, min(1.0, (break_even - spot_price) / (long_put_row['strike_price'] - short_put_row['strike_price']) + 0.5))

                        strategy = OptionsStrategy(
                            strategy_id=f"BPS_{underlying}_{long_put_row['strike_price']}_{short_put_row['strike_price']}_{expiry_date}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                            strategy_type=StrategyType.BEAR_PUT_SPREAD,
                            underlying=underlying,
                            legs=[long_leg, short_leg],
                            max_profit=max_profit,
                            max_loss=max_loss,
                            break_even_points=[break_even],
                            probability_of_profit=prob_profit,
                            net_premium=-net_premium,  # Negative for debit
                            margin_required=max_loss,  # Margin is max loss for debit spreads
                            risk_reward_ratio=max_profit / max_loss if max_loss > 0 else float('inf'),
                            target_profit=max_profit * 0.8,
                            stop_loss=max_loss * 0.5,
                            created_at=datetime.now(),
                            entry_conditions=[{"indicator": "Price", "operator": "below_resistance", "value": spot_price * 1.02}],
                            exit_conditions=[{"indicator": "Price", "operator": "above_support", "value": spot_price * 0.95}]
                        )
                        strategies.append(strategy)
            return strategies
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate bear put spread strategies: {e}")
            return []

    async def _generate_iron_condor_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                             spot_price: float) -> List[OptionsStrategy]:
        """Generate iron condor strategies"""
        # Four-leg strategy: sell call spread + sell put spread
        # Implementation would create four-leg strategies
        return []

    # Implemented methods for remaining strategies
    async def _generate_protective_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate protective put strategies"""
        try:
            strategies = []

            # Get OTM put strikes (below spot price)
            otm_put_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'PE')

            for strike in otm_put_strikes[:5]:  # Limit to top 5
                put_df = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                )

                if put_df.height == 0:
                    continue

                put_dict = put_df.row(0, named=True)

                # Create protective put leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = float('inf')  # Unlimited upside from stock
                max_loss = spot_price - strike + put_dict['ltp']  # Limited downside
                break_even = spot_price + put_dict['ltp']
                prob_profit = 0.6  # Higher probability due to protection

                strategy = OptionsStrategy(
                    strategy_id=f"PP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.PROTECTIVE_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_dict['ltp'],
                    margin_required=put_dict['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 3,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Trend", "operator": "bullish", "value": "strong"}],
                    exit_conditions=[{"indicator": "Protection", "operator": "activated", "value": strike}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate protective put strategies: {e}")
            return []

    async def _generate_otm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM long call strategies"""
        try:
            strategies = []

            # Get OTM call strikes (above spot price)
            otm_call_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'CE')

            for strike in otm_call_strikes[:10]:  # Limit to top 10
                call_df = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                )

                if call_df.height == 0:
                    continue

                call_dict = call_df.row(0, named=True)

                # Create OTM call leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{call_dict['expiry_date'].replace('-', '')}{int(strike)}CE",
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call_dict['expiry_date'],
                    quantity=1,
                    premium=call_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = float('inf')
                max_loss = call_dict['ltp']
                break_even = strike + call_dict['ltp']
                prob_profit = 0.4  # Lower probability for OTM

                strategy = OptionsStrategy(
                    strategy_id=f"OTMLC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.OTM_LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call_dict['ltp'],
                    margin_required=call_dict['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 3,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Momentum", "operator": "strong_bullish", "value": 0.7}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 2}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate OTM long call strategies: {e}")
            return []

    async def _generate_otm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM long put strategies"""
        try:
            strategies = []

            # Get OTM put strikes (below spot price)
            otm_put_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'PE')

            for strike in otm_put_strikes[:10]:  # Limit to top 10
                put_df = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                )

                if put_df.height == 0:
                    continue

                put_dict = put_df.row(0, named=True)

                # Create OTM put leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = strike - put_dict['ltp']
                max_loss = put_dict['ltp']
                break_even = strike - put_dict['ltp']
                prob_profit = 0.4  # Lower probability for OTM

                strategy = OptionsStrategy(
                    strategy_id=f"OTMLP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.OTM_LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_dict['ltp'],
                    margin_required=put_dict['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Momentum", "operator": "strong_bearish", "value": -0.7}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 2}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate OTM long put strategies: {e}")
            return []

    async def _generate_far_otm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Far OTM long call strategies"""
        try:
            strategies = []

            # Get OTM call strikes and select far ones (higher strikes)
            otm_call_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'CE')
            far_otm_strikes = [s for s in otm_call_strikes if s > spot_price * 1.05]  # At least 5% OTM

            for strike in far_otm_strikes[:8]:  # Limit to top 8
                call_df = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                )

                if call_df.height == 0:
                    continue

                call_dict = call_df.row(0, named=True)

                # Create Far OTM call leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{call_dict['expiry_date'].replace('-', '')}{int(strike)}CE",
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call_dict['expiry_date'],
                    quantity=1,
                    premium=call_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = float('inf')
                max_loss = call_dict['ltp']
                break_even = strike + call_dict['ltp']
                prob_profit = 0.25  # Lower probability for far OTM

                strategy = OptionsStrategy(
                    strategy_id=f"FOTMLC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.FAR_OTM_LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call_dict['ltp'],
                    margin_required=call_dict['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 5,
                    stop_loss=max_loss * 0.3,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Breakout", "operator": "strong_bullish", "value": 0.8}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 3}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate far OTM long call strategies: {e}")
            return []

    async def _generate_far_otm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Far OTM long put strategies"""
        try:
            strategies = []

            # Get OTM put strikes and select far ones (lower strikes)
            otm_put_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'PE')
            far_otm_strikes = [s for s in otm_put_strikes if s < spot_price * 0.95]  # At least 5% OTM

            for strike in far_otm_strikes[:8]:  # Limit to top 8
                put_df = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                )

                if put_df.height == 0:
                    continue

                put_dict = put_df.row(0, named=True)

                # Create Far OTM put leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = strike - put_dict['ltp']
                max_loss = put_dict['ltp']
                break_even = strike - put_dict['ltp']
                prob_profit = 0.25  # Lower probability for far OTM

                strategy = OptionsStrategy(
                    strategy_id=f"FOTMLP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.FAR_OTM_LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_dict['ltp'],
                    margin_required=put_dict['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 4,
                    stop_loss=max_loss * 0.3,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Breakdown", "operator": "strong_bearish", "value": -0.8}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 3}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate far OTM long put strategies: {e}")
            return []

    async def _generate_deep_otm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Deep OTM long call strategies"""
        try:
            strategies = []

            # Get OTM call strikes and select deep ones (very high strikes)
            otm_call_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'CE')
            deep_otm_strikes = [s for s in otm_call_strikes if s > spot_price * 1.10]  # At least 10% OTM

            for strike in deep_otm_strikes[:5]:  # Limit to top 5
                call_df = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                )

                if call_df.height == 0:
                    continue

                call_dict = call_df.row(0, named=True)

                # Create Deep OTM call leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{call_dict['expiry_date'].replace('-', '')}{int(strike)}CE",
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call_dict['expiry_date'],
                    quantity=1,
                    premium=call_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = float('inf')
                max_loss = call_dict['ltp']
                break_even = strike + call_dict['ltp']
                prob_profit = 0.15  # Very low probability for deep OTM

                strategy = OptionsStrategy(
                    strategy_id=f"DOTMLC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.DEEP_OTM_LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call_dict['ltp'],
                    margin_required=call_dict['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 10,
                    stop_loss=max_loss * 0.2,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Event", "operator": "major_bullish", "value": "earnings_surprise"}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 5}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate deep OTM long call strategies: {e}")
            return []

    async def _generate_deep_otm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Deep OTM long put strategies"""
        try:
            strategies = []

            # Get OTM put strikes and select deep ones (very low strikes)
            otm_put_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'PE')
            deep_otm_strikes = [s for s in otm_put_strikes if s < spot_price * 0.90]  # At least 10% OTM

            for strike in deep_otm_strikes[:5]:  # Limit to top 5
                put_df = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                )

                if put_df.height == 0:
                    continue

                put_dict = put_df.row(0, named=True)

                # Create Deep OTM put leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                max_profit = strike - put_dict['ltp']
                max_loss = put_dict['ltp']
                break_even = strike - put_dict['ltp']
                prob_profit = 0.15  # Very low probability for deep OTM

                strategy = OptionsStrategy(
                    strategy_id=f"DOTMLP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.DEEP_OTM_LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_dict['ltp'],
                    margin_required=put_dict['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 8,
                    stop_loss=max_loss * 0.2,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Event", "operator": "major_bearish", "value": "market_crash"}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 5}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate deep OTM long put strategies: {e}")
            return []

    async def _generate_intraday_scalping_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate intraday scalping call strategies"""
        try:
            strategies = []

            # Get ATM and near ATM call strikes for scalping
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes[:3]:  # Limit to top 3 ATM strikes
                call_df = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                )

                if call_df.height == 0:
                    continue

                call_dict = call_df.row(0, named=True)

                # Create intraday scalping call leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{call_dict['expiry_date'].replace('-', '')}{int(strike)}CE",
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call_dict['expiry_date'],
                    quantity=1,
                    premium=call_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics for scalping
                max_profit = float('inf')
                max_loss = call_dict['ltp']
                break_even = strike + call_dict['ltp']
                prob_profit = 0.6  # Higher probability for scalping

                strategy = OptionsStrategy(
                    strategy_id=f"IDSC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.INTRADAY_SCALPING_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call_dict['ltp'],
                    margin_required=call_dict['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 0.3,  # Quick 30% profit target
                    stop_loss=max_loss * 0.2,     # Quick 20% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": "spike", "value": 2.0}],
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day", "value": "15:20"}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate intraday scalping call strategies: {e}")
            return []

    async def _generate_intraday_scalping_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate intraday scalping put strategies"""
        try:
            strategies = []

            # Get ATM and near ATM put strikes for scalping
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes[:3]:  # Limit to top 3 ATM strikes
                put_df = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                )

                if put_df.height == 0:
                    continue

                put_dict = put_df.row(0, named=True)

                # Create intraday scalping put leg
                leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics for scalping
                max_profit = strike - put_dict['ltp']
                max_loss = put_dict['ltp']
                break_even = strike - put_dict['ltp']
                prob_profit = 0.6  # Higher probability for scalping

                strategy = OptionsStrategy(
                    strategy_id=f"IDSP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.INTRADAY_SCALPING_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_dict['ltp'],
                    margin_required=put_dict['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 0.3,  # Quick 30% profit target
                    stop_loss=max_loss * 0.2,     # Quick 20% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": "spike", "value": 2.0}],
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day", "value": "15:20"}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate intraday scalping put strategies: {e}")
            return []

    async def _generate_gamma_scalping_long_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate gamma scalping long strategies"""
        try:
            strategies = []

            # Get ATM strikes for gamma scalping
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes[:2]:  # Limit to top 2 ATM strikes
                # Create straddle for gamma scalping
                call_df = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                )

                put_df = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                )

                if call_df.height == 0 or put_df.height == 0:
                    continue

                call_dict = call_df.row(0, named=True)
                put_dict = put_df.row(0, named=True)

                # Create gamma scalping legs (long straddle)
                call_leg = OptionsLeg(
                    symbol=f"{underlying}{call_dict['expiry_date'].replace('-', '')}{int(strike)}CE",
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call_dict['expiry_date'],
                    quantity=1,
                    premium=call_dict['ltp'],
                    underlying=underlying
                )

                put_leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                total_premium = call_dict['ltp'] + put_dict['ltp']
                max_profit = float('inf')  # Unlimited profit potential
                max_loss = total_premium
                break_even_upper = strike + total_premium
                break_even_lower = strike - total_premium
                prob_profit = 0.4  # Needs significant movement

                strategy = OptionsStrategy(
                    strategy_id=f"GSL_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.GAMMA_SCALPING_LONG,
                    underlying=underlying,
                    legs=[call_leg, put_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=-total_premium,
                    margin_required=total_premium,
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 0.5,  # 50% profit target
                    stop_loss=max_loss * 0.3,     # 30% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volatility", "operator": "low", "value": 0.15}],
                    exit_conditions=[{"indicator": "Volatility", "operator": "expansion", "value": 0.25}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate gamma scalping long strategies: {e}")
            return []

    async def _generate_volatility_breakout_long_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate volatility breakout long strategies"""
        try:
            strategies = []

            # Get ATM strikes for volatility breakout
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes[:2]:  # Limit to top 2 ATM strikes
                # Create long straddle for volatility breakout
                call_df = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                )

                put_df = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                )

                if call_df.height == 0 or put_df.height == 0:
                    continue

                call_dict = call_df.row(0, named=True)
                put_dict = put_df.row(0, named=True)

                # Create volatility breakout legs
                call_leg = OptionsLeg(
                    symbol=f"{underlying}{call_dict['expiry_date'].replace('-', '')}{int(strike)}CE",
                    option_type='CE',
                    strike_price=strike,
                    expiry_date=call_dict['expiry_date'],
                    quantity=1,
                    premium=call_dict['ltp'],
                    underlying=underlying
                )

                put_leg = OptionsLeg(
                    symbol=f"{underlying}{put_dict['expiry_date'].replace('-', '')}{int(strike)}PE",
                    option_type='PE',
                    strike_price=strike,
                    expiry_date=put_dict['expiry_date'],
                    quantity=1,
                    premium=put_dict['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                total_premium = call_dict['ltp'] + put_dict['ltp']
                max_profit = float('inf')  # Unlimited profit potential
                max_loss = total_premium
                break_even_upper = strike + total_premium
                break_even_lower = strike - total_premium
                prob_profit = 0.45  # Higher probability for breakout

                strategy = OptionsStrategy(
                    strategy_id=f"VBL_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.VOLATILITY_BREAKOUT_LONG,
                    underlying=underlying,
                    legs=[call_leg, put_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=-total_premium,
                    margin_required=total_premium,
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 1.0,  # 100% profit target
                    stop_loss=max_loss * 0.4,     # 40% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volatility", "operator": "compression", "value": 0.12}],
                    exit_conditions=[{"indicator": "Breakout", "operator": "confirmed", "value": "either_direction"}]
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate volatility breakout long strategies: {e}")
            return []

    async def _generate_reverse_iron_condor_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate reverse iron condor strategies (net debit)"""
        return []

    async def _generate_reverse_iron_butterfly_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate reverse iron butterfly strategies (net debit)"""
        return []

    async def _generate_bull_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate bull call spread strategies (net debit)"""
        return []
    
    async def _generate_bear_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate bear put spread strategies (net debit)"""
        return []

    async def _generate_call_calendar_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate call calendar spread strategies (net debit)"""
        try:
            strategies = []
            calls = option_chain.filter(pl.col('option_type') == 'CE')

            # Group calls by strike price
            for strike in calls['strike_price'].unique().sort().to_list():
                strike_calls = calls.filter(pl.col('strike_price') == strike).sort('expiry_date')

                if strike_calls.height < 2:
                    continue

                # Iterate through possible combinations for short (near) and long (far) expiry calls
                for i in range(strike_calls.height):
                    short_call_row = strike_calls.row(i, named=True)
                    for j in range(i + 1, strike_calls.height):
                        long_call_row = strike_calls.row(j, named=True)

                        # Ensure short expiry is earlier than long expiry
                        if short_call_row['expiry_date'] >= long_call_row['expiry_date']:
                            continue

                        # Create legs
                        short_leg = OptionsLeg(
                            symbol=short_call_row['symbol'],
                            option_type='CE',
                            strike_price=strike,
                            expiry_date=short_call_row['expiry_date'],
                            quantity=-1,  # Short position
                            premium=short_call_row['ltp'],
                            underlying=underlying
                        )

                        long_leg = OptionsLeg(
                            symbol=long_call_row['symbol'],
                            option_type='CE',
                            strike_price=strike,
                            expiry_date=long_call_row['expiry_date'],
                            quantity=1,
                            premium=long_call_row['ltp'],
                            underlying=underlying
                        )

                        # Calculate strategy metrics
                        net_premium = long_call_row['ltp'] - short_call_row['ltp']
                        if net_premium <= 0:  # Must be a debit spread
                            continue

                        max_profit = float('inf')  # Theoretical unlimited profit if underlying moves significantly
                        max_loss = net_premium
                        break_even_upper = strike + net_premium
                        break_even_lower = strike - net_premium # Simplified, actual BE is complex for calendar

                        # Estimate probability of profit (simplified)
                        prob_profit = 0.5 # Calendar spreads profit from time decay and volatility

                        strategy = OptionsStrategy(
                            strategy_id=f"CCS_{underlying}_{strike}_{short_call_row['expiry_date']}_{long_call_row['expiry_date']}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                            strategy_type=StrategyType.CALL_CALENDAR_SPREAD,
                            underlying=underlying,
                            legs=[short_leg, long_leg],
                            max_profit=max_profit,
                            max_loss=max_loss,
                            break_even_points=[break_even_lower, break_even_upper],
                            probability_of_profit=prob_profit,
                            net_premium=-net_premium,  # Negative for debit
                            margin_required=max_loss,  # Margin is max loss for debit spreads
                            risk_reward_ratio=float('inf'),
                            target_profit=max_loss * 1.5,
                            stop_loss=max_loss * 0.75,
                            created_at=datetime.now(),
                            entry_conditions=[{"indicator": "IV", "operator": "low_for_near_term", "value": 0.1}],
                            exit_conditions=[{"indicator": "Time", "operator": "near_short_expiry"}]
                        )
                        strategies.append(strategy)
            return strategies
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate call calendar spread strategies: {e}")
            return []

    async def _generate_put_calendar_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate put calendar spread strategies (net debit)"""
        try:
            strategies = []
            puts = option_chain.filter(pl.col('option_type') == 'PE')

            # Group puts by strike price
            for strike in puts['strike_price'].unique().sort().to_list():
                strike_puts = puts.filter(pl.col('strike_price') == strike).sort('expiry_date')

                if strike_puts.height < 2:
                    continue

                # Iterate through possible combinations for short (near) and long (far) expiry puts
                for i in range(strike_puts.height):
                    short_put_row = strike_puts.row(i, named=True)
                    for j in range(i + 1, strike_puts.height):
                        long_put_row = strike_puts.row(j, named=True)

                        # Ensure short expiry is earlier than long expiry
                        if short_put_row['expiry_date'] >= long_put_row['expiry_date']:
                            continue

                        # Create legs
                        short_leg = OptionsLeg(
                            symbol=short_put_row['symbol'],
                            option_type='PE',
                            strike_price=strike,
                            expiry_date=short_put_row['expiry_date'],
                            quantity=-1,  # Short position
                            premium=short_put_row['ltp'],
                            underlying=underlying
                        )

                        long_leg = OptionsLeg(
                            symbol=long_put_row['symbol'],
                            option_type='PE',
                            strike_price=strike,
                            expiry_date=long_put_row['expiry_date'],
                            quantity=1,
                            premium=long_put_row['ltp'],
                            underlying=underlying
                        )

                        # Calculate strategy metrics
                        net_premium = long_put_row['ltp'] - short_put_row['ltp']
                        if net_premium <= 0:  # Must be a debit spread
                            continue

                        max_profit = float('inf')  # Theoretical unlimited profit if underlying moves significantly
                        max_loss = net_premium
                        break_even_upper = strike + net_premium # Simplified, actual BE is complex for calendar
                        break_even_lower = strike - net_premium

                        # Estimate probability of profit (simplified)
                        prob_profit = 0.5 # Calendar spreads profit from time decay and volatility

                        strategy = OptionsStrategy(
                            strategy_id=f"PCS_{underlying}_{strike}_{short_put_row['expiry_date']}_{long_put_row['expiry_date']}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                            strategy_type=StrategyType.PUT_CALENDAR_SPREAD,
                            underlying=underlying,
                            legs=[short_leg, long_leg],
                            max_profit=max_profit,
                            max_loss=max_loss,
                            break_even_points=[break_even_lower, break_even_upper],
                            probability_of_profit=prob_profit,
                            net_premium=-net_premium,  # Negative for debit
                            margin_required=max_loss,  # Margin is max loss for debit spreads
                            risk_reward_ratio=float('inf'),
                            target_profit=max_loss * 1.5,
                            stop_loss=max_loss * 0.75,
                            created_at=datetime.now(),
                            entry_conditions=[{"indicator": "IV", "operator": "low_for_near_term", "value": 0.1}],
                            exit_conditions=[{"indicator": "Time", "operator": "near_short_expiry"}]
                        )
                        strategies.append(strategy)
            return strategies
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate put calendar spread strategies: {e}")
            return []

    async def _generate_diagonal_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate diagonal call spread strategies (net debit)"""
        try:
            strategies = []
            calls = option_chain.filter(pl.col('option_type') == 'CE').sort(['expiry_date', 'strike_price'])

            # Group calls by expiry date
            expiry_dates = calls['expiry_date'].unique().sort().to_list()

            if len(expiry_dates) < 2:
                return []

            for i in range(len(expiry_dates)):
                near_expiry = expiry_dates[i]
                near_calls = calls.filter(pl.col('expiry_date') == near_expiry)

                for j in range(i + 1, len(expiry_dates)):
                    far_expiry = expiry_dates[j]
                    far_calls = calls.filter(pl.col('expiry_date') == far_expiry)

                    # Iterate through combinations of strikes
                    for long_call_row in far_calls.iter_rows(named=True):
                        # Find a short call with a higher strike and near expiry
                        short_call_candidates = near_calls.filter(
                            pl.col('strike_price') > long_call_row['strike_price']
                        ).sort('strike_price')

                        if short_call_candidates.height == 0:
                            continue

                        # Pick the nearest OTM short call
                        short_call_row = short_call_candidates.row(0, named=True)

                        # Create legs
                        long_leg = OptionsLeg(
                            symbol=long_call_row['symbol'],
                            option_type='CE',
                            strike_price=long_call_row['strike_price'],
                            expiry_date=far_expiry,
                            quantity=1,
                            premium=long_call_row['ltp'],
                            underlying=underlying
                        )

                        short_leg = OptionsLeg(
                            symbol=short_call_row['symbol'],
                            option_type='CE',
                            strike_price=short_call_row['strike_price'],
                            expiry_date=near_expiry,
                            quantity=-1,  # Short position
                            premium=short_call_row['ltp'],
                            underlying=underlying
                        )

                        # Calculate strategy metrics
                        net_premium = long_call_row['ltp'] - short_call_row['ltp']
                        if net_premium <= 0:  # Must be a debit spread
                            continue

                        max_profit = float('inf')  # Theoretical unlimited profit
                        max_loss = net_premium
                        break_even = long_call_row['strike_price'] + net_premium # Simplified

                        # Estimate probability of profit (simplified)
                        prob_profit = 0.45 # Depends on directional bias and time decay

                        strategy = OptionsStrategy(
                            strategy_id=f"DCS_{underlying}_{long_call_row['strike_price']}_{short_call_row['strike_price']}_{near_expiry}_{far_expiry}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                            strategy_type=StrategyType.DIAGONAL_CALL_SPREAD,
                            underlying=underlying,
                            legs=[long_leg, short_leg],
                            max_profit=max_profit,
                            max_loss=max_loss,
                            break_even_points=[break_even],
                            probability_of_profit=prob_profit,
                            net_premium=-net_premium,  # Negative for debit
                            margin_required=max_loss,  # Margin is max loss for debit spreads
                            risk_reward_ratio=float('inf'),
                            target_profit=max_loss * 1.5,
                            stop_loss=max_loss * 0.75,
                            created_at=datetime.now(),
                            entry_conditions=[{"indicator": "Trend", "operator": "bullish", "value": "moderate"}],
                            exit_conditions=[{"indicator": "Time", "operator": "near_short_expiry"}]
                        )
                        strategies.append(strategy)
            return strategies
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate diagonal call spread strategies: {e}")
            return []

    async def _generate_diagonal_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate diagonal put spread strategies (net debit)"""
        try:
            strategies = []
            puts = option_chain.filter(pl.col('option_type') == 'PE').sort(['expiry_date', 'strike_price'], descending=[False, True])

            # Group puts by expiry date
            expiry_dates = puts['expiry_date'].unique().sort().to_list()

            if len(expiry_dates) < 2:
                return []

            for i in range(len(expiry_dates)):
                near_expiry = expiry_dates[i]
                near_puts = puts.filter(pl.col('expiry_date') == near_expiry)

                for j in range(i + 1, len(expiry_dates)):
                    far_expiry = expiry_dates[j]
                    far_puts = puts.filter(pl.col('expiry_date') == far_expiry)

                    # Iterate through combinations of strikes
                    for long_put_row in far_puts.iter_rows(named=True):
                        # Find a short put with a lower strike and near expiry
                        short_put_candidates = near_puts.filter(
                            pl.col('strike_price') < long_put_row['strike_price']
                        ).sort('strike_price', descending=True)

                        if short_put_candidates.height == 0:
                            continue

                        # Pick the nearest OTM short put
                        short_put_row = short_put_candidates.row(0, named=True)

                        # Create legs
                        long_leg = OptionsLeg(
                            symbol=long_put_row['symbol'],
                            option_type='PE',
                            strike_price=long_put_row['strike_price'],
                            expiry_date=far_expiry,
                            quantity=1,
                            premium=long_put_row['ltp'],
                            underlying=underlying
                        )

                        short_leg = OptionsLeg(
                            symbol=short_put_row['symbol'],
                            option_type='PE',
                            strike_price=short_put_row['strike_price'],
                            expiry_date=near_expiry,
                            quantity=-1,  # Short position
                            premium=short_put_row['ltp'],
                            underlying=underlying
                        )

                        # Calculate strategy metrics
                        net_premium = long_put_row['ltp'] - short_put_row['ltp']
                        if net_premium <= 0:  # Must be a debit spread
                            continue

                        max_profit = float('inf')  # Theoretical unlimited profit
                        max_loss = net_premium
                        break_even = long_put_row['strike_price'] - net_premium # Simplified

                        # Estimate probability of profit (simplified)
                        prob_profit = 0.45 # Depends on directional bias and time decay

                        strategy = OptionsStrategy(
                            strategy_id=f"DPS_{underlying}_{long_put_row['strike_price']}_{short_put_row['strike_price']}_{near_expiry}_{far_expiry}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                            strategy_type=StrategyType.DIAGONAL_PUT_SPREAD,
                            underlying=underlying,
                            legs=[long_leg, short_leg],
                            max_profit=max_profit,
                            max_loss=max_loss,
                            break_even_points=[break_even],
                            probability_of_profit=prob_profit,
                            net_premium=-net_premium,  # Negative for debit
                            margin_required=max_loss,  # Margin is max loss for debit spreads
                            risk_reward_ratio=float('inf'),
                            target_profit=max_loss * 1.5,
                            stop_loss=max_loss * 0.75,
                            created_at=datetime.now(),
                            entry_conditions=[{"indicator": "Trend", "operator": "bearish", "value": "moderate"}],
                            exit_conditions=[{"indicator": "Time", "operator": "near_short_expiry"}]
                        )
                        strategies.append(strategy)
            return strategies
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate diagonal put spread strategies: {e}")
            return []

    async def _generate_ratio_call_backspread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate ratio call backspread strategies (net debit)"""
        strategies = []
        try:
            # Filter for call options
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            if calls.height == 0:
                return strategies

            # Get strikes around spot price
            strikes = sorted(calls['strike_price'].unique().to_list())
            spot_strikes = [s for s in strikes if abs(s - spot_price) / spot_price <= 0.15]

            if len(spot_strikes) < 3:
                return strategies

            for i in range(len(spot_strikes) - 2):
                lower_strike = spot_strikes[i]
                middle_strike = spot_strikes[i + 1]
                higher_strike = spot_strikes[i + 2]

                # Get option data
                lower_call = calls.filter(pl.col('strike_price') == lower_strike).to_dicts()
                middle_call = calls.filter(pl.col('strike_price') == middle_strike).to_dicts()
                higher_call = calls.filter(pl.col('strike_price') == higher_strike).to_dicts()

                if not (lower_call and middle_call and higher_call):
                    continue

                lower_call, middle_call, higher_call = lower_call[0], middle_call[0], higher_call[0]

                # Ratio call backspread: Sell 1 lower strike, Buy 2 higher strikes
                # Create legs
                sell_leg = OptionsLeg(
                    symbol=lower_call['symbol'],
                    option_type='CE',
                    strike_price=lower_strike,
                    expiry_date=lower_call.get('expiry_date', '2024-01-25'),
                    quantity=-1,  # Short
                    premium=lower_call['ltp'],
                    underlying=underlying
                )

                buy_leg = OptionsLeg(
                    symbol=higher_call['symbol'],
                    option_type='CE',
                    strike_price=higher_strike,
                    expiry_date=higher_call.get('expiry_date', '2024-01-25'),
                    quantity=2,  # Long 2 contracts
                    premium=higher_call['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                net_premium = -lower_call['ltp'] + 2 * higher_call['ltp']  # Net debit
                max_loss = abs(net_premium)
                max_profit = float('inf')  # Unlimited upside potential
                break_even = lower_strike + (higher_strike - lower_strike) + net_premium

                # Probability of profit (simplified)
                prob_profit = 0.25  # Lower probability due to need for significant upward move

                strategy = OptionsStrategy(
                    strategy_id=f"RCB_{underlying}_{lower_strike}_{higher_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.RATIO_CALL_BACKSPREAD,
                    underlying=underlying,
                    legs=[sell_leg, buy_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=net_premium,
                    margin_required=max_loss,
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 3,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Momentum", "operator": "strong_bullish", "value": 0.8}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 2}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ratio call backspread strategies: {e}")

        return strategies

    async def _generate_ratio_put_backspread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate ratio put backspread strategies (net debit)"""
        strategies = []
        try:
            # Filter for put options
            puts = option_chain.filter(pl.col('option_type') == 'PE')
            if puts.height == 0:
                return strategies

            # Get strikes around spot price
            strikes = sorted(puts['strike_price'].unique().to_list(), reverse=True)
            spot_strikes = [s for s in strikes if abs(s - spot_price) / spot_price <= 0.15]

            if len(spot_strikes) < 3:
                return strategies

            for i in range(len(spot_strikes) - 2):
                higher_strike = spot_strikes[i]
                middle_strike = spot_strikes[i + 1]
                lower_strike = spot_strikes[i + 2]

                # Get option data
                higher_put = puts.filter(pl.col('strike_price') == higher_strike).to_dicts()
                lower_put = puts.filter(pl.col('strike_price') == lower_strike).to_dicts()

                if not (higher_put and lower_put):
                    continue

                higher_put, lower_put = higher_put[0], lower_put[0]

                # Ratio put backspread: Sell 1 higher strike, Buy 2 lower strikes
                sell_leg = OptionsLeg(
                    symbol=higher_put['symbol'],
                    option_type='PE',
                    strike_price=higher_strike,
                    expiry_date=higher_put.get('expiry_date', '2024-01-25'),
                    quantity=-1,  # Short
                    premium=higher_put['ltp'],
                    underlying=underlying
                )

                buy_leg = OptionsLeg(
                    symbol=lower_put['symbol'],
                    option_type='PE',
                    strike_price=lower_strike,
                    expiry_date=lower_put.get('expiry_date', '2024-01-25'),
                    quantity=2,  # Long 2 contracts
                    premium=lower_put['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                net_premium = -higher_put['ltp'] + 2 * lower_put['ltp']  # Net debit
                max_loss = abs(net_premium)
                max_profit = float('inf')  # Unlimited downside potential
                break_even = higher_strike - (higher_strike - lower_strike) - net_premium

                # Probability of profit (simplified)
                prob_profit = 0.25  # Lower probability due to need for significant downward move

                strategy = OptionsStrategy(
                    strategy_id=f"RPB_{underlying}_{higher_strike}_{lower_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.RATIO_PUT_BACKSPREAD,
                    underlying=underlying,
                    legs=[sell_leg, buy_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=net_premium,
                    margin_required=max_loss,
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 3,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Momentum", "operator": "strong_bearish", "value": -0.8}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_loss * 2}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ratio put backspread strategies: {e}")

        return strategies

    async def _generate_collar_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate collar strategies (often net zero or small debit)"""
        strategies = []
        try:
            # Filter for calls and puts
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            puts = option_chain.filter(pl.col('option_type') == 'PE')

            if calls.height == 0 or puts.height == 0:
                return strategies

            # Get OTM call strikes (above spot)
            call_strikes = [s for s in calls['strike_price'].unique().to_list() if s > spot_price * 1.02]
            # Get OTM put strikes (below spot)
            put_strikes = [s for s in puts['strike_price'].unique().to_list() if s < spot_price * 0.98]

            if not call_strikes or not put_strikes:
                return strategies

            for call_strike in call_strikes[:3]:  # Limit to first 3 strikes
                for put_strike in put_strikes[:3]:
                    # Get option data
                    call_data = calls.filter(pl.col('strike_price') == call_strike).to_dicts()
                    put_data = puts.filter(pl.col('strike_price') == put_strike).to_dicts()

                    if not (call_data and put_data):
                        continue

                    call_data, put_data = call_data[0], put_data[0]

                    # Collar: Long stock + Long put + Short call (protective put + covered call)
                    # For options-only collar: Long ATM call + Long put + Short OTM call

                    # Buy protective put
                    put_leg = OptionsLeg(
                        symbol=put_data['symbol'],
                        option_type='PE',
                        strike_price=put_strike,
                        expiry_date=put_data.get('expiry_date', '2024-01-25'),
                        quantity=1,  # Long
                        premium=put_data['ltp'],
                        underlying=underlying
                    )

                    # Sell covered call
                    call_leg = OptionsLeg(
                        symbol=call_data['symbol'],
                        option_type='CE',
                        strike_price=call_strike,
                        expiry_date=call_data.get('expiry_date', '2024-01-25'),
                        quantity=-1,  # Short
                        premium=call_data['ltp'],
                        underlying=underlying
                    )

                    # Calculate strategy metrics
                    net_premium = put_data['ltp'] - call_data['ltp']  # Often near zero
                    max_profit = call_strike - spot_price - net_premium
                    max_loss = spot_price - put_strike + net_premium
                    break_even = spot_price + net_premium

                    # Probability of profit
                    prob_profit = 0.6  # Moderate probability

                    strategy = OptionsStrategy(
                        strategy_id=f"COL_{underlying}_{put_strike}_{call_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                        strategy_type=StrategyType.COLLAR,
                        underlying=underlying,
                        legs=[put_leg, call_leg],
                        max_profit=max_profit,
                        max_loss=max_loss,
                        break_even_points=[break_even],
                        probability_of_profit=prob_profit,
                        net_premium=net_premium,
                        margin_required=max(abs(net_premium), max_loss),
                        risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                        target_profit=max_profit * 0.8,
                        stop_loss=max_loss * 0.5,
                        created_at=datetime.now(),
                        entry_conditions=[{"indicator": "Volatility", "operator": "moderate", "value": 0.2}],
                        exit_conditions=[{"indicator": "Time", "operator": "days_to_expiry", "value": 7}]
                    )

                    strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate collar strategies: {e}")

        return strategies

    async def _generate_synthetic_long_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate synthetic long strategies"""
        strategies = []
        try:
            # Filter for calls and puts
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            puts = option_chain.filter(pl.col('option_type') == 'PE')

            if calls.height == 0 or puts.height == 0:
                return strategies

            # Get ATM strikes
            atm_strikes = [s for s in calls['strike_price'].unique().to_list()
                          if abs(s - spot_price) / spot_price <= 0.02]

            for strike in atm_strikes:
                # Get option data
                call_data = calls.filter(pl.col('strike_price') == strike).to_dicts()
                put_data = puts.filter(pl.col('strike_price') == strike).to_dicts()

                if not (call_data and put_data):
                    continue

                call_data, put_data = call_data[0], put_data[0]

                # Synthetic long: Long call + Short put (same strike)
                call_leg = OptionsLeg(
                    symbol=call_data['symbol'],
                        option_type='CE',
                        strike_price=strike,
                        expiry_date=call_data.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=call_data['ltp'],
                    underlying=underlying
                )

                put_leg = OptionsLeg(
                    symbol=put_data['symbol'],
                        option_type='PE',
                        strike_price=strike,
                        expiry_date=put_data.get('expiry_date', '2024-01-25'),
                    quantity=-1,  # Short
                    premium=put_data['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                net_premium = call_data['ltp'] - put_data['ltp']  # Net debit/credit
                max_profit = float('inf')  # Unlimited upside
                max_loss = strike + net_premium  # If stock goes to zero
                break_even = strike + net_premium

                # Probability of profit
                prob_profit = 0.5  # Similar to long stock

                strategy = OptionsStrategy(
                    strategy_id=f"SYN_LONG_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.SYNTHETIC_LONG,
                    underlying=underlying,
                    legs=[call_leg, put_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=net_premium,
                    margin_required=max_loss,
                    risk_reward_ratio=float('inf'),
                    target_profit=spot_price * 0.1,  # 10% target
                    stop_loss=spot_price * 0.05,  # 5% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Trend", "operator": "bullish", "value": 0.6}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": spot_price * 0.08}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate synthetic long strategies: {e}")

        return strategies

    async def _generate_synthetic_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate synthetic long call strategies"""
        strategies = []
        try:
            # Filter for calls and puts
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            puts = option_chain.filter(pl.col('option_type') == 'PE')

            if calls.height == 0 or puts.height == 0:
                return strategies

            # Get strikes around spot price
            strikes = [s for s in calls['strike_price'].unique().to_list()
                      if abs(s - spot_price) / spot_price <= 0.05]

            for strike in strikes:
                # Get put data for synthetic call
                put_data = puts.filter(pl.col('strike_price') == strike).to_dicts()

                if not put_data:
                    continue

                put_data = put_data[0]

                # Synthetic call: Long stock + Long put (protective put)
                # For options-only: Long ATM call equivalent using puts
                # Synthetic call = Long underlying + Long put

                put_leg = OptionsLeg(
                    symbol=put_data['symbol'],
                        option_type='PE',
                        strike_price=strike,
                        expiry_date=put_data.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=put_data['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics (assuming we own underlying)
                cost_basis = spot_price + put_data['ltp']  # Stock price + put premium
                max_profit = float('inf')  # Unlimited upside
                max_loss = put_data['ltp']  # Premium paid for put
                break_even = spot_price + put_data['ltp']

                # Probability of profit
                prob_profit = 0.55  # Slightly better than 50/50 due to upward bias

                strategy = OptionsStrategy(
                    strategy_id=f"SYN_CALL_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.SYNTHETIC_CALL,
                    underlying=underlying,
                    legs=[put_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_data['ltp'],  # Cost of protection
                    margin_required=put_data['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=spot_price * 0.1,  # 10% target
                    stop_loss=put_data['ltp'] * 0.5,  # 50% of premium
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Trend", "operator": "bullish", "value": 0.6}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": spot_price * 0.08}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate synthetic call strategies: {e}")

        return strategies

    async def _generate_synthetic_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate synthetic long put strategies"""
        strategies = []
        try:
            # Filter for calls and puts
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            puts = option_chain.filter(pl.col('option_type') == 'PE')

            if calls.height == 0 or puts.height == 0:
                return strategies

            # Get strikes around spot price
            strikes = [s for s in calls['strike_price'].unique().to_list()
                      if abs(s - spot_price) / spot_price <= 0.05]

            for strike in strikes:
                # Get call data for synthetic put
                call_data = calls.filter(pl.col('strike_price') == strike).to_dicts()

                if not call_data:
                    continue

                call_data = call_data[0]

                # Synthetic put: Short stock + Long call
                # For options-only: Long put equivalent using calls

                call_leg = OptionsLeg(
                    symbol=call_data['symbol'],
                        option_type='CE',
                        strike_price=strike,
                        expiry_date=call_data.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=call_data['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics (assuming we short underlying)
                max_profit = strike - call_data['ltp']  # Strike minus premium
                max_loss = float('inf')  # Unlimited upside risk when short
                break_even = strike - call_data['ltp']

                # Probability of profit
                prob_profit = 0.45  # Slightly less than 50/50 due to upward bias

                strategy = OptionsStrategy(
                    strategy_id=f"SYN_PUT_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.SYNTHETIC_PUT,
                    underlying=underlying,
                    legs=[call_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call_data['ltp'],  # Cost of call
                    margin_required=call_data['ltp'],
                    risk_reward_ratio=max_profit / call_data['ltp'] if call_data['ltp'] > 0 else 0,
                    target_profit=max_profit * 0.8,
                    stop_loss=call_data['ltp'] * 0.5,  # 50% of premium
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Trend", "operator": "bearish", "value": -0.6}],
                    exit_conditions=[{"indicator": "Profit", "operator": "target_reached", "value": max_profit * 0.6}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate synthetic put strategies: {e}")

        return strategies

    async def _generate_butterfly_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate butterfly spread strategies (net debit)"""
        strategies = []
        try:
            # Filter for calls and puts
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            puts = option_chain.filter(pl.col('option_type') == 'PE')

            # Generate call butterfly spreads
            if calls.height > 0:
                strategies.extend(await self._generate_call_butterfly_spreads(underlying, calls, spot_price))

            # Generate put butterfly spreads
            if puts.height > 0:
                strategies.extend(await self._generate_put_butterfly_spreads(underlying, puts, spot_price))

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate butterfly spread strategies: {e}")

        return strategies

    async def _generate_call_butterfly_spreads(self, underlying: str, calls: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate call butterfly spread strategies"""
        strategies = []
        try:
            # Get strikes around spot price
            strikes = sorted(calls['strike_price'].unique().to_list())
            atm_strikes = [s for s in strikes if abs(s - spot_price) / spot_price <= 0.1]

            if len(atm_strikes) < 3:
                return strategies

            for i in range(len(atm_strikes) - 2):
                lower_strike = atm_strikes[i]
                middle_strike = atm_strikes[i + 1]
                upper_strike = atm_strikes[i + 2]

                # Get option data
                lower_call = calls.filter(pl.col('strike_price') == lower_strike).to_dicts()
                middle_call = calls.filter(pl.col('strike_price') == middle_strike).to_dicts()
                upper_call = calls.filter(pl.col('strike_price') == upper_strike).to_dicts()

                if not (lower_call and middle_call and upper_call):
                    continue

                lower_call, middle_call, upper_call = lower_call[0], middle_call[0], upper_call[0]

                # Long butterfly: Buy 1 lower, Sell 2 middle, Buy 1 upper
                lower_leg = OptionsLeg(
                    symbol=lower_call['symbol'],
                    option_type='CE',
                    strike_price=lower_strike,
                    expiry_date=lower_call.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=lower_call['ltp'],
                    underlying=underlying
                )

                middle_leg = OptionsLeg(
                    symbol=middle_call['symbol'],
                    option_type='CE',
                    strike_price=middle_strike,
                    expiry_date=middle_call.get('expiry_date', '2024-01-25'),
                    quantity=-2,  # Short 2
                    premium=middle_call['ltp'],
                    underlying=underlying
                )

                upper_leg = OptionsLeg(
                    symbol=upper_call['symbol'],
                    option_type='CE',
                    strike_price=upper_strike,
                    expiry_date=upper_call.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=upper_call['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                net_premium = lower_call['ltp'] - 2 * middle_call['ltp'] + upper_call['ltp']
                max_loss = abs(net_premium)
                max_profit = (middle_strike - lower_strike) - abs(net_premium)
                break_even_lower = lower_strike + abs(net_premium)
                break_even_upper = upper_strike - abs(net_premium)

                # Probability of profit (range-bound strategy)
                prob_profit = 0.4  # Moderate probability

                strategy = OptionsStrategy(
                    strategy_id=f"CALL_BF_{underlying}_{lower_strike}_{middle_strike}_{upper_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.BUTTERFLY_SPREAD,
                    underlying=underlying,
                    legs=[lower_leg, middle_leg, upper_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=net_premium,
                    margin_required=max_loss,
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_profit * 0.8,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volatility", "operator": "low", "value": 0.15}],
                    exit_conditions=[{"indicator": "Time", "operator": "days_to_expiry", "value": 7}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate call butterfly spreads: {e}")

        return strategies

    async def _generate_put_butterfly_spreads(self, underlying: str, puts: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate put butterfly spread strategies"""
        strategies = []
        try:
            # Get strikes around spot price
            strikes = sorted(puts['strike_price'].unique().to_list(), reverse=True)
            atm_strikes = [s for s in strikes if abs(s - spot_price) / spot_price <= 0.1]

            if len(atm_strikes) < 3:
                return strategies

            for i in range(len(atm_strikes) - 2):
                upper_strike = atm_strikes[i]
                middle_strike = atm_strikes[i + 1]
                lower_strike = atm_strikes[i + 2]

                # Get option data
                upper_put = puts.filter(pl.col('strike_price') == upper_strike).to_dicts()
                middle_put = puts.filter(pl.col('strike_price') == middle_strike).to_dicts()
                lower_put = puts.filter(pl.col('strike_price') == lower_strike).to_dicts()

                if not (upper_put and middle_put and lower_put):
                    continue

                upper_put, middle_put, lower_put = upper_put[0], middle_put[0], lower_put[0]

                # Long butterfly: Buy 1 upper, Sell 2 middle, Buy 1 lower
                upper_leg = OptionsLeg(
                    symbol=upper_put['symbol'],
                    option_type='PE',
                    strike_price=upper_strike,
                    expiry_date=upper_put.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=upper_put['ltp'],
                    underlying=underlying
                )

                middle_leg = OptionsLeg(
                    symbol=middle_put['symbol'],
                    option_type='PE',
                    strike_price=middle_strike,
                    expiry_date=middle_put.get('expiry_date', '2024-01-25'),
                    quantity=-2,  # Short 2
                    premium=middle_put['ltp'],
                    underlying=underlying
                )

                lower_leg = OptionsLeg(
                    symbol=lower_put['symbol'],
                    option_type='PE',
                    strike_price=lower_strike,
                    expiry_date=lower_put.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=lower_put['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                net_premium = upper_put['ltp'] - 2 * middle_put['ltp'] + lower_put['ltp']
                max_loss = abs(net_premium)
                max_profit = (upper_strike - middle_strike) - abs(net_premium)
                break_even_lower = middle_strike - abs(net_premium)
                break_even_upper = middle_strike + abs(net_premium)

                # Probability of profit (range-bound strategy)
                prob_profit = 0.4  # Moderate probability

                strategy = OptionsStrategy(
                    strategy_id=f"PUT_BF_{underlying}_{upper_strike}_{middle_strike}_{lower_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.BUTTERFLY_SPREAD,
                    underlying=underlying,
                    legs=[upper_leg, middle_leg, lower_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=net_premium,
                    margin_required=max_loss,
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_profit * 0.8,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volatility", "operator": "low", "value": 0.15}],
                    exit_conditions=[{"indicator": "Time", "operator": "days_to_expiry", "value": 7}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate put butterfly spreads: {e}")

        return strategies

    async def _generate_condor_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate condor spread strategies (net debit)"""
        strategies = []
        try:
            # Filter for calls and puts
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            puts = option_chain.filter(pl.col('option_type') == 'PE')

            # Generate call condor spreads
            if calls.height > 0:
                strategies.extend(await self._generate_call_condor_spreads(underlying, calls, spot_price))

            # Generate put condor spreads
            if puts.height > 0:
                strategies.extend(await self._generate_put_condor_spreads(underlying, puts, spot_price))

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate condor spread strategies: {e}")

        return strategies

    async def _generate_call_condor_spreads(self, underlying: str, calls: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate call condor spread strategies"""
        strategies = []
        try:
            # Get strikes around spot price
            strikes = sorted(calls['strike_price'].unique().to_list())
            spot_strikes = [s for s in strikes if abs(s - spot_price) / spot_price <= 0.15]

            if len(spot_strikes) < 4:
                return strategies

            for i in range(len(spot_strikes) - 3):
                strike1 = spot_strikes[i]      # Lowest
                strike2 = spot_strikes[i + 1]  # Lower middle
                strike3 = spot_strikes[i + 2]  # Upper middle
                strike4 = spot_strikes[i + 3]  # Highest

                # Get option data
                call1 = calls.filter(pl.col('strike_price') == strike1).to_dicts()
                call2 = calls.filter(pl.col('strike_price') == strike2).to_dicts()
                call3 = calls.filter(pl.col('strike_price') == strike3).to_dicts()
                call4 = calls.filter(pl.col('strike_price') == strike4).to_dicts()

                if not (call1 and call2 and call3 and call4):
                    continue

                call1, call2, call3, call4 = call1[0], call2[0], call3[0], call4[0]

                # Long condor: Buy 1 lowest, Sell 1 lower middle, Sell 1 upper middle, Buy 1 highest
                leg1 = OptionsLeg(
                    symbol=call1['symbol'],
                    option_type='CE',
                    strike_price=strike1,
                    expiry_date=lower_call.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=call1['ltp'],
                    underlying=underlying
                )

                leg2 = OptionsLeg(
                    symbol=call2['symbol'],
                    option_type='CE',
                    strike_price=strike2,
                    expiry_date=lower_call.get('expiry_date', '2024-01-25'),
                    quantity=-1,  # Short
                    premium=call2['ltp'],
                    underlying=underlying
                )

                leg3 = OptionsLeg(
                    symbol=call3['symbol'],
                    option_type='CE',
                    strike_price=strike3,
                    expiry_date=lower_call.get('expiry_date', '2024-01-25'),
                    quantity=-1,  # Short
                    premium=call3['ltp'],
                    underlying=underlying
                )

                leg4 = OptionsLeg(
                    symbol=call4['symbol'],
                    option_type='CE',
                    strike_price=strike4,
                    expiry_date=lower_call.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=call4['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                net_premium = call1['ltp'] - call2['ltp'] - call3['ltp'] + call4['ltp']
                max_loss = abs(net_premium)
                max_profit = (strike2 - strike1) - abs(net_premium)
                break_even_lower = strike1 + abs(net_premium)
                break_even_upper = strike4 - abs(net_premium)

                # Probability of profit (range-bound strategy)
                prob_profit = 0.35  # Lower than butterfly due to wider range

                strategy = OptionsStrategy(
                    strategy_id=f"CALL_CONDOR_{underlying}_{strike1}_{strike2}_{strike3}_{strike4}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.CONDOR_SPREAD,
                    underlying=underlying,
                    legs=[leg1, leg2, leg3, leg4],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=net_premium,
                    margin_required=max_loss,
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_profit * 0.8,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volatility", "operator": "low", "value": 0.15}],
                    exit_conditions=[{"indicator": "Time", "operator": "days_to_expiry", "value": 10}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate call condor spreads: {e}")

        return strategies

    async def _generate_put_condor_spreads(self, underlying: str, puts: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate put condor spread strategies"""
        strategies = []
        try:
            # Get strikes around spot price
            strikes = sorted(puts['strike_price'].unique().to_list(), reverse=True)
            spot_strikes = [s for s in strikes if abs(s - spot_price) / spot_price <= 0.15]

            if len(spot_strikes) < 4:
                return strategies

            for i in range(len(spot_strikes) - 3):
                strike1 = spot_strikes[i]      # Highest
                strike2 = spot_strikes[i + 1]  # Upper middle
                strike3 = spot_strikes[i + 2]  # Lower middle
                strike4 = spot_strikes[i + 3]  # Lowest

                # Get option data
                put1 = puts.filter(pl.col('strike_price') == strike1).to_dicts()
                put2 = puts.filter(pl.col('strike_price') == strike2).to_dicts()
                put3 = puts.filter(pl.col('strike_price') == strike3).to_dicts()
                put4 = puts.filter(pl.col('strike_price') == strike4).to_dicts()

                if not (put1 and put2 and put3 and put4):
                    continue

                put1, put2, put3, put4 = put1[0], put2[0], put3[0], put4[0]

                # Long condor: Buy 1 highest, Sell 1 upper middle, Sell 1 lower middle, Buy 1 lowest
                leg1 = OptionsLeg(
                    symbol=put1['symbol'],
                    option_type='PE',
                    strike_price=strike1,
                    expiry_date=lower_call.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=put1['ltp'],
                    underlying=underlying
                )

                leg2 = OptionsLeg(
                    symbol=put2['symbol'],
                    option_type='PE',
                    strike_price=strike2,
                    expiry_date=lower_call.get('expiry_date', '2024-01-25'),
                    quantity=-1,  # Short
                    premium=put2['ltp'],
                    underlying=underlying
                )

                leg3 = OptionsLeg(
                    symbol=put3['symbol'],
                    option_type='PE',
                    strike_price=strike3,
                    expiry_date=lower_call.get('expiry_date', '2024-01-25'),
                    quantity=-1,  # Short
                    premium=put3['ltp'],
                    underlying=underlying
                )

                leg4 = OptionsLeg(
                    symbol=put4['symbol'],
                    option_type='PE',
                    strike_price=strike4,
                    expiry_date=lower_call.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=put4['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                net_premium = put1['ltp'] - put2['ltp'] - put3['ltp'] + put4['ltp']
                max_loss = abs(net_premium)
                max_profit = (strike2 - strike3) - abs(net_premium)
                break_even_lower = strike3 - abs(net_premium)
                break_even_upper = strike2 + abs(net_premium)

                # Probability of profit (range-bound strategy)
                prob_profit = 0.35  # Lower than butterfly due to wider range

                strategy = OptionsStrategy(
                    strategy_id=f"PUT_CONDOR_{underlying}_{strike1}_{strike2}_{strike3}_{strike4}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.CONDOR_SPREAD,
                    underlying=underlying,
                    legs=[leg1, leg2, leg3, leg4],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=net_premium,
                    margin_required=max_loss,
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_profit * 0.8,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volatility", "operator": "low", "value": 0.15}],
                    exit_conditions=[{"indicator": "Time", "operator": "days_to_expiry", "value": 10}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate put condor spreads: {e}")

        return strategies

    async def _generate_christmas_tree_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate christmas tree strategies (net debit)"""
        strategies = []
        try:
            # Filter for calls and puts
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            puts = option_chain.filter(pl.col('option_type') == 'PE')

            # Generate call christmas tree spreads
            if calls.height > 0:
                strategies.extend(await self._generate_call_christmas_tree(underlying, calls, spot_price))

            # Generate put christmas tree spreads
            if puts.height > 0:
                strategies.extend(await self._generate_put_christmas_tree(underlying, puts, spot_price))

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate christmas tree strategies: {e}")

        return strategies

    async def _generate_call_christmas_tree(self, underlying: str, calls: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate call christmas tree strategies"""
        strategies = []
        try:
            # Get strikes around spot price
            strikes = sorted(calls['strike_price'].unique().to_list())
            spot_strikes = [s for s in strikes if abs(s - spot_price) / spot_price <= 0.1]

            if len(spot_strikes) < 3:
                return strategies

            for i in range(len(spot_strikes) - 2):
                lower_strike = spot_strikes[i]
                middle_strike = spot_strikes[i + 1]
                upper_strike = spot_strikes[i + 2]

                # Get option data
                lower_call = calls.filter(pl.col('strike_price') == lower_strike).to_dicts()
                middle_call = calls.filter(pl.col('strike_price') == middle_strike).to_dicts()
                upper_call = calls.filter(pl.col('strike_price') == upper_strike).to_dicts()

                if not (lower_call and middle_call and upper_call):
                    continue

                lower_call, middle_call, upper_call = lower_call[0], middle_call[0], upper_call[0]

                # Christmas tree: Buy 1 lower, Sell 3 middle, Buy 2 upper
                # This creates an asymmetric butterfly with higher profit potential on one side
                lower_leg = OptionsLeg(
                    symbol=lower_call['symbol'],
                    option_type='CE',
                    strike_price=lower_strike,
                    expiry_date=lower_call.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=lower_call['ltp'],
                    underlying=underlying
                )

                middle_leg = OptionsLeg(
                    symbol=middle_call['symbol'],
                    option_type='CE',
                    strike_price=middle_strike,
                    expiry_date=middle_call.get('expiry_date', '2024-01-25'),
                    quantity=-3,  # Short 3
                    premium=middle_call['ltp'],
                    underlying=underlying
                )

                upper_leg = OptionsLeg(
                    symbol=upper_call['symbol'],
                    option_type='CE',
                    strike_price=upper_strike,
                    expiry_date=upper_call.get('expiry_date', '2024-01-25'),
                    quantity=2,  # Long 2
                    premium=upper_call['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                net_premium = lower_call['ltp'] - 3 * middle_call['ltp'] + 2 * upper_call['ltp']
                max_loss = abs(net_premium)
                max_profit = (middle_strike - lower_strike) - abs(net_premium)
                break_even = lower_strike + abs(net_premium)

                # Probability of profit (directional bias)
                prob_profit = 0.3  # Lower probability due to complex structure

                strategy = OptionsStrategy(
                    strategy_id=f"XMAS_CALL_{underlying}_{lower_strike}_{middle_strike}_{upper_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.CHRISTMAS_TREE,
                    underlying=underlying,
                    legs=[lower_leg, middle_leg, upper_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=net_premium,
                    margin_required=max_loss,
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_profit * 0.8,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volatility", "operator": "moderate", "value": 0.2}],
                    exit_conditions=[{"indicator": "Time", "operator": "days_to_expiry", "value": 7}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate call christmas tree: {e}")

        return strategies

    async def _generate_put_christmas_tree(self, underlying: str, puts: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate put christmas tree strategies"""
        strategies = []
        try:
            # Get strikes around spot price
            strikes = sorted(puts['strike_price'].unique().to_list(), reverse=True)
            spot_strikes = [s for s in strikes if abs(s - spot_price) / spot_price <= 0.1]

            if len(spot_strikes) < 3:
                return strategies

            for i in range(len(spot_strikes) - 2):
                upper_strike = spot_strikes[i]
                middle_strike = spot_strikes[i + 1]
                lower_strike = spot_strikes[i + 2]

                # Get option data
                upper_put = puts.filter(pl.col('strike_price') == upper_strike).to_dicts()
                middle_put = puts.filter(pl.col('strike_price') == middle_strike).to_dicts()
                lower_put = puts.filter(pl.col('strike_price') == lower_strike).to_dicts()

                if not (upper_put and middle_put and lower_put):
                    continue

                upper_put, middle_put, lower_put = upper_put[0], middle_put[0], lower_put[0]

                # Christmas tree: Buy 1 upper, Sell 3 middle, Buy 2 lower
                upper_leg = OptionsLeg(
                    symbol=upper_put['symbol'],
                    option_type='PE',
                    strike_price=upper_strike,
                    expiry_date=upper_put.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=upper_put['ltp'],
                    underlying=underlying
                )

                middle_leg = OptionsLeg(
                    symbol=middle_put['symbol'],
                    option_type='PE',
                    strike_price=middle_strike,
                    expiry_date=middle_put.get('expiry_date', '2024-01-25'),
                    quantity=-3,  # Short 3
                    premium=middle_put['ltp'],
                    underlying=underlying
                )

                lower_leg = OptionsLeg(
                    symbol=lower_put['symbol'],
                    option_type='PE',
                    strike_price=lower_strike,
                    expiry_date=lower_put.get('expiry_date', '2024-01-25'),
                    quantity=2,  # Long 2
                    premium=lower_put['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                net_premium = upper_put['ltp'] - 3 * middle_put['ltp'] + 2 * lower_put['ltp']
                max_loss = abs(net_premium)
                max_profit = (upper_strike - middle_strike) - abs(net_premium)
                break_even = upper_strike - abs(net_premium)

                # Probability of profit (directional bias)
                prob_profit = 0.3  # Lower probability due to complex structure

                strategy = OptionsStrategy(
                    strategy_id=f"XMAS_PUT_{underlying}_{upper_strike}_{middle_strike}_{lower_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.CHRISTMAS_TREE,
                    underlying=underlying,
                    legs=[upper_leg, middle_leg, lower_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=net_premium,
                    margin_required=max_loss,
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_profit * 0.8,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volatility", "operator": "moderate", "value": 0.2}],
                    exit_conditions=[{"indicator": "Time", "operator": "days_to_expiry", "value": 7}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate put christmas tree: {e}")

        return strategies

    async def _generate_weekly_expiry_straddle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate weekly expiry straddle strategies"""
        strategies = []
        try:
            # Filter for weekly expiry options (assuming weekly expiry is within 7 days)
            # For this implementation, we'll use all available options and filter by ATM strikes
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            puts = option_chain.filter(pl.col('option_type') == 'PE')

            if calls.height == 0 or puts.height == 0:
                return strategies

            # Get ATM strikes (within 2% of spot price)
            atm_strikes = []
            all_strikes = calls['strike_price'].unique().to_list()
            for strike in all_strikes:
                if abs(strike - spot_price) / spot_price <= 0.02:
                    atm_strikes.append(strike)

            for strike in atm_strikes:
                # Get call and put data for the same strike
                call_data = calls.filter(pl.col('strike_price') == strike).to_dicts()
                put_data = puts.filter(pl.col('strike_price') == strike).to_dicts()

                if not (call_data and put_data):
                    continue

                call_data, put_data = call_data[0], put_data[0]

                # Create straddle legs
                call_leg = OptionsLeg(
                    symbol=call_data['symbol'],
                        option_type='CE',
                        strike_price=strike,
                        expiry_date=call_data.get('expiry_date', '2024-01-25'),  # Weekly expiry
                    quantity=1,  # Long
                    premium=call_data['ltp'],
                    underlying=underlying
                )

                put_leg = OptionsLeg(
                    symbol=put_data['symbol'],
                        option_type='PE',
                        strike_price=strike,
                        expiry_date=put_data.get('expiry_date', '2024-01-25'),  # Weekly expiry
                    quantity=1,  # Long
                    premium=put_data['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                total_premium = call_data['ltp'] + put_data['ltp']
                max_loss = total_premium
                max_profit = float('inf')  # Unlimited profit potential
                break_even_upper = strike + total_premium
                break_even_lower = strike - total_premium

                # Higher probability for weekly expiry due to time decay acceleration
                prob_profit = 0.45  # Higher than monthly due to faster time decay

                strategy = OptionsStrategy(
                    strategy_id=f"WEEKLY_STRADDLE_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.WEEKLY_EXPIRY_STRADDLE,
                    underlying=underlying,
                    legs=[call_leg, put_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=-total_premium,  # Net debit
                    margin_required=total_premium,
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 1.5,  # 150% return target
                    stop_loss=max_loss * 0.5,  # 50% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "IV", "operator": "low", "value": 0.15},
                                    {"indicator": "Event", "operator": "expected", "value": "weekly_expiry"}],
                    exit_conditions=[{"indicator": "Time", "operator": "days_to_expiry", "value": 2}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate weekly expiry straddle strategies: {e}")

        return strategies

    async def _generate_monthly_expiry_strangle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate monthly expiry strangle strategies"""
        strategies = []
        try:
            # Filter for monthly expiry options
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            puts = option_chain.filter(pl.col('option_type') == 'PE')

            if calls.height == 0 or puts.height == 0:
                return strategies

            # Get OTM call strikes (above spot price)
            call_strikes = [s for s in calls['strike_price'].unique().to_list()
                           if s > spot_price * 1.02 and s <= spot_price * 1.1]

            # Get OTM put strikes (below spot price)
            put_strikes = [s for s in puts['strike_price'].unique().to_list()
                          if s < spot_price * 0.98 and s >= spot_price * 0.9]

            if not call_strikes or not put_strikes:
                return strategies

            # Create strangle combinations
            for call_strike in call_strikes[:3]:  # Limit to first 3 strikes
                for put_strike in put_strikes[:3]:
                    # Get option data
                    call_data = calls.filter(pl.col('strike_price') == call_strike).to_dicts()
                    put_data = puts.filter(pl.col('strike_price') == put_strike).to_dicts()

                    if not (call_data and put_data):
                        continue

                    call_data, put_data = call_data[0], put_data[0]

                    # Create strangle legs
                    call_leg = OptionsLeg(
                        symbol=call_data['symbol'],
                        option_type='CE',
                        strike_price=call_strike,
                        expiry_date=call_data.get('expiry_date', '2024-02-29'),  # Monthly expiry
                        quantity=1,  # Long
                        premium=call_data['ltp'],
                        underlying=underlying
                    )

                    put_leg = OptionsLeg(
                        symbol=put_data['symbol'],
                        option_type='PE',
                        strike_price=put_strike,
                        expiry_date=put_data.get('expiry_date', '2024-02-29'),  # Monthly expiry
                        quantity=1,  # Long
                        premium=put_data['ltp'],
                        underlying=underlying
                    )

                    # Calculate strategy metrics
                    total_premium = call_data['ltp'] + put_data['ltp']
                    max_loss = total_premium
                    max_profit = float('inf')  # Unlimited profit potential
                    break_even_upper = call_strike + total_premium
                    break_even_lower = put_strike - total_premium

                    # Probability of profit for monthly strangle
                    prob_profit = 0.35  # Lower than straddle due to wider strikes

                    strategy = OptionsStrategy(
                        strategy_id=f"MONTHLY_STRANGLE_{underlying}_{put_strike}_{call_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                        strategy_type=StrategyType.MONTHLY_EXPIRY_STRANGLE,
                        underlying=underlying,
                        legs=[call_leg, put_leg],
                        max_profit=max_profit,
                        max_loss=max_loss,
                        break_even_points=[break_even_lower, break_even_upper],
                        probability_of_profit=prob_profit,
                        net_premium=-total_premium,  # Net debit
                        margin_required=total_premium,
                        risk_reward_ratio=float('inf'),
                        target_profit=max_loss * 2,  # 200% return target
                        stop_loss=max_loss * 0.5,  # 50% stop loss
                        created_at=datetime.now(),
                        entry_conditions=[{"indicator": "IV", "operator": "low", "value": 0.18},
                                        {"indicator": "Event", "operator": "expected", "value": "monthly_expiry"}],
                        exit_conditions=[{"indicator": "Time", "operator": "days_to_expiry", "value": 7}]
                    )

                    strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate monthly expiry strangle strategies: {e}")

        return strategies

    async def _generate_banknifty_butterfly_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate BankNifty butterfly strategies (assuming net debit)"""
        strategies = []
        try:
            # This strategy is specifically for BankNifty
            if underlying != "BANKNIFTY":
                return strategies

            # Filter for calls and puts
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            puts = option_chain.filter(pl.col('option_type') == 'PE')

            if calls.height == 0 and puts.height == 0:
                return strategies

            # BankNifty specific strike intervals (typically 100 points)
            # Get strikes around spot price with BankNifty-specific logic
            if calls.height > 0:
                call_strikes = sorted(calls['strike_price'].unique().to_list())
                # Filter for strikes within 500 points of spot (BankNifty specific range)
                banknifty_call_strikes = [s for s in call_strikes
                                        if abs(s - spot_price) <= 500 and s % 100 == 0]

                if len(banknifty_call_strikes) >= 3:
                    strategies.extend(await self._generate_banknifty_call_butterfly(
                        underlying, calls, spot_price, banknifty_call_strikes))

            if puts.height > 0:
                put_strikes = sorted(puts['strike_price'].unique().to_list(), reverse=True)
                # Filter for strikes within 500 points of spot (BankNifty specific range)
                banknifty_put_strikes = [s for s in put_strikes
                                       if abs(s - spot_price) <= 500 and s % 100 == 0]

                if len(banknifty_put_strikes) >= 3:
                    strategies.extend(await self._generate_banknifty_put_butterfly(
                        underlying, puts, spot_price, banknifty_put_strikes))

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate BankNifty butterfly strategies: {e}")

        return strategies

    async def _generate_banknifty_call_butterfly(self, underlying: str, calls: pl.DataFrame,
                                               spot_price: float, strikes: List[float]) -> List[OptionsStrategy]:
        """Generate BankNifty call butterfly strategies"""
        strategies = []
        try:
            for i in range(len(strikes) - 2):
                lower_strike = strikes[i]
                middle_strike = strikes[i + 1]
                upper_strike = strikes[i + 2]

                # Ensure proper 100-point intervals for BankNifty
                if (middle_strike - lower_strike != 100 or
                    upper_strike - middle_strike != 100):
                    continue

                # Get option data
                lower_call = calls.filter(pl.col('strike_price') == lower_strike).to_dicts()
                middle_call = calls.filter(pl.col('strike_price') == middle_strike).to_dicts()
                upper_call = calls.filter(pl.col('strike_price') == upper_strike).to_dicts()

                if not (lower_call and middle_call and upper_call):
                    continue

                lower_call, middle_call, upper_call = lower_call[0], middle_call[0], upper_call[0]

                # BankNifty butterfly: Buy 1 lower, Sell 2 middle, Buy 1 upper
                lower_leg = OptionsLeg(
                    symbol=lower_call['symbol'],
                    option_type='CE',
                    strike_price=lower_strike,
                    expiry_date=lower_call.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=lower_call['ltp'],
                    underlying=underlying
                )

                middle_leg = OptionsLeg(
                    symbol=middle_call['symbol'],
                    option_type='CE',
                    strike_price=middle_strike,
                    expiry_date=middle_call.get('expiry_date', '2024-01-25'),
                    quantity=-2,  # Short 2
                    premium=middle_call['ltp'],
                    underlying=underlying
                )

                upper_leg = OptionsLeg(
                    symbol=upper_call['symbol'],
                    option_type='CE',
                    strike_price=upper_strike,
                    expiry_date=upper_call.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=upper_call['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                net_premium = lower_call['ltp'] - 2 * middle_call['ltp'] + upper_call['ltp']
                max_loss = abs(net_premium)
                max_profit = 100 - abs(net_premium)  # 100 points max for BankNifty
                break_even_lower = lower_strike + abs(net_premium)
                break_even_upper = upper_strike - abs(net_premium)

                # BankNifty specific probability (higher volatility)
                prob_profit = 0.42  # Slightly higher due to BankNifty characteristics

                strategy = OptionsStrategy(
                    strategy_id=f"BN_CALL_BF_{underlying}_{lower_strike}_{middle_strike}_{upper_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.BANKNIFTY_BUTTERFLY,
                    underlying=underlying,
                    legs=[lower_leg, middle_leg, upper_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=net_premium,
                    margin_required=max_loss,
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_profit * 0.8,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volatility", "operator": "moderate", "value": 0.18},
                                    {"indicator": "Index", "operator": "banknifty_range", "value": "consolidation"}],
                    exit_conditions=[{"indicator": "Time", "operator": "days_to_expiry", "value": 5}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate BankNifty call butterfly: {e}")

        return strategies

    async def _generate_banknifty_put_butterfly(self, underlying: str, puts: pl.DataFrame,
                                              spot_price: float, strikes: List[float]) -> List[OptionsStrategy]:
        """Generate BankNifty put butterfly strategies"""
        strategies = []
        try:
            for i in range(len(strikes) - 2):
                upper_strike = strikes[i]
                middle_strike = strikes[i + 1]
                lower_strike = strikes[i + 2]

                # Ensure proper 100-point intervals for BankNifty
                if (upper_strike - middle_strike != 100 or
                    middle_strike - lower_strike != 100):
                    continue

                # Get option data
                upper_put = puts.filter(pl.col('strike_price') == upper_strike).to_dicts()
                middle_put = puts.filter(pl.col('strike_price') == middle_strike).to_dicts()
                lower_put = puts.filter(pl.col('strike_price') == lower_strike).to_dicts()

                if not (upper_put and middle_put and lower_put):
                    continue

                upper_put, middle_put, lower_put = upper_put[0], middle_put[0], lower_put[0]

                # BankNifty butterfly: Buy 1 upper, Sell 2 middle, Buy 1 lower
                upper_leg = OptionsLeg(
                    symbol=upper_put['symbol'],
                    option_type='PE',
                    strike_price=upper_strike,
                    expiry_date=upper_put.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=upper_put['ltp'],
                    underlying=underlying
                )

                middle_leg = OptionsLeg(
                    symbol=middle_put['symbol'],
                    option_type='PE',
                    strike_price=middle_strike,
                    expiry_date=middle_put.get('expiry_date', '2024-01-25'),
                    quantity=-2,  # Short 2
                    premium=middle_put['ltp'],
                    underlying=underlying
                )

                lower_leg = OptionsLeg(
                    symbol=lower_put['symbol'],
                    option_type='PE',
                    strike_price=lower_strike,
                    expiry_date=lower_put.get('expiry_date', '2024-01-25'),
                    quantity=1,  # Long
                    premium=lower_put['ltp'],
                    underlying=underlying
                )

                # Calculate strategy metrics
                net_premium = upper_put['ltp'] - 2 * middle_put['ltp'] + lower_put['ltp']
                max_loss = abs(net_premium)
                max_profit = 100 - abs(net_premium)  # 100 points max for BankNifty
                break_even_lower = middle_strike - abs(net_premium)
                break_even_upper = middle_strike + abs(net_premium)

                # BankNifty specific probability (higher volatility)
                prob_profit = 0.42  # Slightly higher due to BankNifty characteristics

                strategy = OptionsStrategy(
                    strategy_id=f"BN_PUT_BF_{underlying}_{upper_strike}_{middle_strike}_{lower_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.BANKNIFTY_BUTTERFLY,
                    underlying=underlying,
                    legs=[upper_leg, middle_leg, lower_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=net_premium,
                    margin_required=max_loss,
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_profit * 0.8,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volatility", "operator": "moderate", "value": 0.18},
                                    {"indicator": "Index", "operator": "banknifty_range", "value": "consolidation"}],
                    exit_conditions=[{"indicator": "Time", "operator": "days_to_expiry", "value": 5}]
                )

                strategies.append(strategy)

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate BankNifty put butterfly: {e}")

        return strategies
