[{"strategy_id": "LC_NIFTY_23600.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523600CE", "option_type": "CE", "strike_price": 23600.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23705.0], "probability_of_profit": 0.5518, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24000.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524000CE", "option_type": "CE", "strike_price": 24000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24105.0], "probability_of_profit": 0.5358, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24400.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524400CE", "option_type": "CE", "strike_price": 24400.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24505.0], "probability_of_profit": 0.5198, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24700.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524700CE", "option_type": "CE", "strike_price": 24700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24805.0], "probability_of_profit": 0.5078, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25450.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525450CE", "option_type": "CE", "strike_price": 25450.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25555.0], "probability_of_profit": 0.4778, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26350.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526350CE", "option_type": "CE", "strike_price": 26350.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26455.0], "probability_of_profit": 0.44179999999999997, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26450.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526450CE", "option_type": "CE", "strike_price": 26450.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26555.0], "probability_of_profit": 0.4378, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_27000.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2527000CE", "option_type": "CE", "strike_price": 27000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [27105.0], "probability_of_profit": 0.4158, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23300.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523300CE", "option_type": "CE", "strike_price": 23300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23405.0], "probability_of_profit": 0.5638, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23350.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523350CE", "option_type": "CE", "strike_price": 23350.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23455.0], "probability_of_profit": 0.5618, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23750.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523750CE", "option_type": "CE", "strike_price": 23750.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23855.0], "probability_of_profit": 0.5458, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23850.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523850CE", "option_type": "CE", "strike_price": 23850.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23955.0], "probability_of_profit": 0.5418, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25050.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525050CE", "option_type": "CE", "strike_price": 25050.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25155.0], "probability_of_profit": 0.4938, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25650.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525650CE", "option_type": "CE", "strike_price": 25650.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25755.0], "probability_of_profit": 0.4698, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26800.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526800CE", "option_type": "CE", "strike_price": 26800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26905.0], "probability_of_profit": 0.4238, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_22700.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2522700CE", "option_type": "CE", "strike_price": 22700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [22805.0], "probability_of_profit": 0.5878, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_22850.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2522850CE", "option_type": "CE", "strike_price": 22850.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [22955.0], "probability_of_profit": 0.5818, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_22950.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2522950CE", "option_type": "CE", "strike_price": 22950.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23055.0], "probability_of_profit": 0.5778, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23400.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523400CE", "option_type": "CE", "strike_price": 23400.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23505.0], "probability_of_profit": 0.5598, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24150.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524150CE", "option_type": "CE", "strike_price": 24150.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24255.0], "probability_of_profit": 0.5298, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24200.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524200CE", "option_type": "CE", "strike_price": 24200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24305.0], "probability_of_profit": 0.5278, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24300.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524300CE", "option_type": "CE", "strike_price": 24300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24405.0], "probability_of_profit": 0.5238, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25300.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525300CE", "option_type": "CE", "strike_price": 25300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25405.0], "probability_of_profit": 0.4838, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25500.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525500CE", "option_type": "CE", "strike_price": 25500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25605.0], "probability_of_profit": 0.4758, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25600.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525600CE", "option_type": "CE", "strike_price": 25600.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25705.0], "probability_of_profit": 0.4718, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26050.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526050CE", "option_type": "CE", "strike_price": 26050.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26155.0], "probability_of_profit": 0.4538, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26150.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526150CE", "option_type": "CE", "strike_price": 26150.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26255.0], "probability_of_profit": 0.4498, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26200.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526200CE", "option_type": "CE", "strike_price": 26200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26305.0], "probability_of_profit": 0.4478, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26300.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526300CE", "option_type": "CE", "strike_price": 26300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26405.0], "probability_of_profit": 0.4438, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_27200.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2527200CE", "option_type": "CE", "strike_price": 27200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [27305.0], "probability_of_profit": 0.4078, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23450.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523450CE", "option_type": "CE", "strike_price": 23450.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23555.0], "probability_of_profit": 0.5578, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23550.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523550CE", "option_type": "CE", "strike_price": 23550.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23655.0], "probability_of_profit": 0.5538, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24550.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524550CE", "option_type": "CE", "strike_price": 24550.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24655.0], "probability_of_profit": 0.5138, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25000.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525000CE", "option_type": "CE", "strike_price": 25000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25105.0], "probability_of_profit": 0.4958, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25250.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525250CE", "option_type": "CE", "strike_price": 25250.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25355.0], "probability_of_profit": 0.4858, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25850.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525850CE", "option_type": "CE", "strike_price": 25850.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25955.0], "probability_of_profit": 0.4618, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25900.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525900CE", "option_type": "CE", "strike_price": 25900.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26005.0], "probability_of_profit": 0.4598, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26650.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526650CE", "option_type": "CE", "strike_price": 26650.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26755.0], "probability_of_profit": 0.4298, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_27050.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2527050CE", "option_type": "CE", "strike_price": 27050.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [27155.0], "probability_of_profit": 0.4138, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_27300.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2527300CE", "option_type": "CE", "strike_price": 27300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [27405.0], "probability_of_profit": 0.4038, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_27350.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2527350CE", "option_type": "CE", "strike_price": 27350.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [27455.0], "probability_of_profit": 0.4018, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23250.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523250CE", "option_type": "CE", "strike_price": 23250.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23355.0], "probability_of_profit": 0.5658, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24050.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524050CE", "option_type": "CE", "strike_price": 24050.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24155.0], "probability_of_profit": 0.5338, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24250.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524250CE", "option_type": "CE", "strike_price": 24250.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24355.0], "probability_of_profit": 0.5258, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25100.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525100CE", "option_type": "CE", "strike_price": 25100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25205.0], "probability_of_profit": 0.4918, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25150.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525150CE", "option_type": "CE", "strike_price": 25150.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25255.0], "probability_of_profit": 0.4898, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26100.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526100CE", "option_type": "CE", "strike_price": 26100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26205.0], "probability_of_profit": 0.4518, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26250.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526250CE", "option_type": "CE", "strike_price": 26250.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26355.0], "probability_of_profit": 0.4458, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26700.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526700CE", "option_type": "CE", "strike_price": 26700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26805.0], "probability_of_profit": 0.4278, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26950.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526950CE", "option_type": "CE", "strike_price": 26950.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [27055.0], "probability_of_profit": 0.4178, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_27100.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2527100CE", "option_type": "CE", "strike_price": 27100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [27205.0], "probability_of_profit": 0.4118, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_27250.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2527250CE", "option_type": "CE", "strike_price": 27250.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [27355.0], "probability_of_profit": 0.4058, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_22750.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2522750CE", "option_type": "CE", "strike_price": 22750.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [22855.0], "probability_of_profit": 0.5858, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23050.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523050CE", "option_type": "CE", "strike_price": 23050.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23155.0], "probability_of_profit": 0.5738, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23200.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523200CE", "option_type": "CE", "strike_price": 23200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23305.0], "probability_of_profit": 0.5678, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23500.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523500CE", "option_type": "CE", "strike_price": 23500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23605.0], "probability_of_profit": 0.5558, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23800.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523800CE", "option_type": "CE", "strike_price": 23800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23905.0], "probability_of_profit": 0.5438, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24750.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524750CE", "option_type": "CE", "strike_price": 24750.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24855.0], "probability_of_profit": 0.5058, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24800.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524800CE", "option_type": "CE", "strike_price": 24800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24905.0], "probability_of_profit": 0.5038, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24900.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524900CE", "option_type": "CE", "strike_price": 24900.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25005.0], "probability_of_profit": 0.4998, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26500.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526500CE", "option_type": "CE", "strike_price": 26500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26605.0], "probability_of_profit": 0.4358, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26600.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526600CE", "option_type": "CE", "strike_price": 26600.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26705.0], "probability_of_profit": 0.4318, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26850.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526850CE", "option_type": "CE", "strike_price": 26850.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26955.0], "probability_of_profit": 0.4218, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26900.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526900CE", "option_type": "CE", "strike_price": 26900.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [27005.0], "probability_of_profit": 0.4198, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_22600.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2522600CE", "option_type": "CE", "strike_price": 22600.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [22705.0], "probability_of_profit": 0.5918, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_22900.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2522900CE", "option_type": "CE", "strike_price": 22900.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23005.0], "probability_of_profit": 0.5798, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23000.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523000CE", "option_type": "CE", "strike_price": 23000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23105.0], "probability_of_profit": 0.5758, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23700.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523700CE", "option_type": "CE", "strike_price": 23700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23805.0], "probability_of_profit": 0.5478, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.450583", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24450.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524450CE", "option_type": "CE", "strike_price": 24450.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24555.0], "probability_of_profit": 0.5178, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24650.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524650CE", "option_type": "CE", "strike_price": 24650.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24755.0], "probability_of_profit": 0.5098, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24850.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524850CE", "option_type": "CE", "strike_price": 24850.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24955.0], "probability_of_profit": 0.5018, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_22650.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2522650CE", "option_type": "CE", "strike_price": 22650.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [22755.0], "probability_of_profit": 0.5898, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25200.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525200CE", "option_type": "CE", "strike_price": 25200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25305.0], "probability_of_profit": 0.4878, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25400.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525400CE", "option_type": "CE", "strike_price": 25400.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25505.0], "probability_of_profit": 0.4798, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24600.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524600CE", "option_type": "CE", "strike_price": 24600.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24705.0], "probability_of_profit": 0.5118, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24950.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524950CE", "option_type": "CE", "strike_price": 24950.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25055.0], "probability_of_profit": 0.4978, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25550.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525550CE", "option_type": "CE", "strike_price": 25550.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25655.0], "probability_of_profit": 0.4738, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25750.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525750CE", "option_type": "CE", "strike_price": 25750.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25855.0], "probability_of_profit": 0.4658, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25950.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525950CE", "option_type": "CE", "strike_price": 25950.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26055.0], "probability_of_profit": 0.4578, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26000.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526000CE", "option_type": "CE", "strike_price": 26000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26105.0], "probability_of_profit": 0.4558, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_27150.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2527150CE", "option_type": "CE", "strike_price": 27150.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [27255.0], "probability_of_profit": 0.4098, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_22800.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2522800CE", "option_type": "CE", "strike_price": 22800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [22905.0], "probability_of_profit": 0.5838, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23100.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523100CE", "option_type": "CE", "strike_price": 23100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23205.0], "probability_of_profit": 0.5718, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23150.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523150CE", "option_type": "CE", "strike_price": 23150.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23255.0], "probability_of_profit": 0.5698, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23650.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523650CE", "option_type": "CE", "strike_price": 23650.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [23755.0], "probability_of_profit": 0.5498, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23900.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523900CE", "option_type": "CE", "strike_price": 23900.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24005.0], "probability_of_profit": 0.5398000000000001, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_23950.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2523950CE", "option_type": "CE", "strike_price": 23950.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24055.0], "probability_of_profit": 0.5378000000000001, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24100.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524100CE", "option_type": "CE", "strike_price": 24100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24205.0], "probability_of_profit": 0.5318, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24350.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524350CE", "option_type": "CE", "strike_price": 24350.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24455.0], "probability_of_profit": 0.5218, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_24500.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524500CE", "option_type": "CE", "strike_price": 24500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [24605.0], "probability_of_profit": 0.5158, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25350.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525350CE", "option_type": "CE", "strike_price": 25350.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25455.0], "probability_of_profit": 0.4818, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25700.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525700CE", "option_type": "CE", "strike_price": 25700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25805.0], "probability_of_profit": 0.4678, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_25800.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525800CE", "option_type": "CE", "strike_price": 25800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25905.0], "probability_of_profit": 0.4638, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26400.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526400CE", "option_type": "CE", "strike_price": 26400.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26505.0], "probability_of_profit": 0.4398, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26550.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526550CE", "option_type": "CE", "strike_price": 26550.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26655.0], "probability_of_profit": 0.4338, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_26750.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2526750CE", "option_type": "CE", "strike_price": 26750.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [26855.0], "probability_of_profit": 0.4258, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "LC_NIFTY_27400.0_20250818114642", "strategy_type": "long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2527400CE", "option_type": "CE", "strike_price": 27400.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [27505.0], "probability_of_profit": 0.3998, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.451579", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 25250.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 24750.0}]}, {"strategy_id": "ATMLC_NIFTY_24950.0_20250818114642", "strategy_type": "atm_long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2524950CE", "option_type": "CE", "strike_price": 24950.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25055.0], "probability_of_profit": 0.5, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.454582", "entry_conditions": [{"indicator": "Volume", "operator": ">", "value": 10000}], "exit_conditions": [{"indicator": "Time", "operator": "end_of_day"}]}, {"strategy_id": "ATMLC_NIFTY_25000.0_20250818114642", "strategy_type": "atm_long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525000CE", "option_type": "CE", "strike_price": 25000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25105.0], "probability_of_profit": 0.5, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.455582", "entry_conditions": [{"indicator": "Volume", "operator": ">", "value": 10000}], "exit_conditions": [{"indicator": "Time", "operator": "end_of_day"}]}, {"strategy_id": "ATMLC_NIFTY_25050.0_20250818114642", "strategy_type": "atm_long_call", "underlying": "NIFTY", "legs": [{"symbol": "NIFTY28AUG2525050CE", "option_type": "CE", "strike_price": 25050.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "NIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [25155.0], "probability_of_profit": 0.5, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.456586", "entry_conditions": [{"indicator": "Volume", "operator": ">", "value": 10000}], "exit_conditions": [{"indicator": "Time", "operator": "end_of_day"}]}, {"strategy_id": "LC_BANKNIFTY_49500.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2549500CE", "option_type": "CE", "strike_price": 49500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [49605.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_50400.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2550400CE", "option_type": "CE", "strike_price": 50400.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [50505.0], "probability_of_profit": 0.***************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_50500.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2550500CE", "option_type": "CE", "strike_price": 50500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [50605.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_50800.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2550800CE", "option_type": "CE", "strike_price": 50800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [50905.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_50900.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2550900CE", "option_type": "CE", "strike_price": 50900.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [51005.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_51300.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2551300CE", "option_type": "CE", "strike_price": 51300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [51405.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_52000.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2552000CE", "option_type": "CE", "strike_price": 52000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [52105.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_53400.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2553400CE", "option_type": "CE", "strike_price": 53400.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [53505.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_54500.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2554500CE", "option_type": "CE", "strike_price": 54500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [54605.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_54700.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2554700CE", "option_type": "CE", "strike_price": 54700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [54805.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_55400.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2555400CE", "option_type": "CE", "strike_price": 55400.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [55505.0], "probability_of_profit": 0.***************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_59900.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2559900CE", "option_type": "CE", "strike_price": 59900.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [60005.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_60200.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2560200CE", "option_type": "CE", "strike_price": 60200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [60305.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_62000.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2562000CE", "option_type": "CE", "strike_price": 62000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [62105.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_63500.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2563500CE", "option_type": "CE", "strike_price": 63500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [63605.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_48000.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2548000CE", "option_type": "CE", "strike_price": 48000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [48105.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.612582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_51000.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2551000CE", "option_type": "CE", "strike_price": 51000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [51105.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_54100.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2554100CE", "option_type": "CE", "strike_price": 54100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [54205.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_54400.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2554400CE", "option_type": "CE", "strike_price": 54400.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [54505.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_55500.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2555500CE", "option_type": "CE", "strike_price": 55500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [55605.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_57300.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2557300CE", "option_type": "CE", "strike_price": 57300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [57405.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_58100.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2558100CE", "option_type": "CE", "strike_price": 58100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [58205.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_59600.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2559600CE", "option_type": "CE", "strike_price": 59600.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [59705.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_60800.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2560800CE", "option_type": "CE", "strike_price": 60800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [60905.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_61500.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2561500CE", "option_type": "CE", "strike_price": 61500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [61605.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_61800.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2561800CE", "option_type": "CE", "strike_price": 61800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [61905.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_51400.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2551400CE", "option_type": "CE", "strike_price": 51400.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [51505.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_51700.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2551700CE", "option_type": "CE", "strike_price": 51700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [51805.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_51900.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2551900CE", "option_type": "CE", "strike_price": 51900.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [52005.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_52200.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2552200CE", "option_type": "CE", "strike_price": 52200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [52305.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_52600.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2552600CE", "option_type": "CE", "strike_price": 52600.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [52705.0], "probability_of_profit": 0.***************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_52800.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2552800CE", "option_type": "CE", "strike_price": 52800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [52905.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_53300.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2553300CE", "option_type": "CE", "strike_price": 53300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [53405.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_53500.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2553500CE", "option_type": "CE", "strike_price": 53500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [53605.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_53600.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2553600CE", "option_type": "CE", "strike_price": 53600.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [53705.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_54000.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2554000CE", "option_type": "CE", "strike_price": 54000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [54105.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_55100.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2555100CE", "option_type": "CE", "strike_price": 55100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [55205.0], "probability_of_profit": 0.***************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_56800.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2556800CE", "option_type": "CE", "strike_price": 56800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [56905.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_57400.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2557400CE", "option_type": "CE", "strike_price": 57400.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [57505.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_58300.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2558300CE", "option_type": "CE", "strike_price": 58300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [58405.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_58900.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2558900CE", "option_type": "CE", "strike_price": 58900.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [59005.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_60400.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2560400CE", "option_type": "CE", "strike_price": 60400.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [60505.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_50100.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2550100CE", "option_type": "CE", "strike_price": 50100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [50205.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_50700.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2550700CE", "option_type": "CE", "strike_price": 50700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [50805.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_51200.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2551200CE", "option_type": "CE", "strike_price": 51200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [51305.0], "probability_of_profit": 0.***************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_52300.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2552300CE", "option_type": "CE", "strike_price": 52300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [52405.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_52500.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2552500CE", "option_type": "CE", "strike_price": 52500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [52605.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_53100.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2553100CE", "option_type": "CE", "strike_price": 53100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [53205.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_53200.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2553200CE", "option_type": "CE", "strike_price": 53200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [53305.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_54300.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2554300CE", "option_type": "CE", "strike_price": 54300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [54405.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_54800.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2554800CE", "option_type": "CE", "strike_price": 54800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [54905.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_56100.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2556100CE", "option_type": "CE", "strike_price": 56100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [56205.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_56200.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2556200CE", "option_type": "CE", "strike_price": 56200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [56305.0], "probability_of_profit": 0.***************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_56700.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2556700CE", "option_type": "CE", "strike_price": 56700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [56805.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_57100.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2557100CE", "option_type": "CE", "strike_price": 57100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [57205.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_57600.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2557600CE", "option_type": "CE", "strike_price": 57600.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [57705.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_58200.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2558200CE", "option_type": "CE", "strike_price": 58200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [58305.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_59000.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2559000CE", "option_type": "CE", "strike_price": 59000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [59105.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_60100.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2560100CE", "option_type": "CE", "strike_price": 60100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [60205.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_50200.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2550200CE", "option_type": "CE", "strike_price": 50200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [50305.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_51800.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2551800CE", "option_type": "CE", "strike_price": 51800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [51905.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_52100.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2552100CE", "option_type": "CE", "strike_price": 52100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [52205.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_53700.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2553700CE", "option_type": "CE", "strike_price": 53700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [53805.0], "probability_of_profit": 0.***************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_53900.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2553900CE", "option_type": "CE", "strike_price": 53900.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [54005.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_54600.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2554600CE", "option_type": "CE", "strike_price": 54600.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [54705.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_58000.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2558000CE", "option_type": "CE", "strike_price": 58000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [58105.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_60300.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2560300CE", "option_type": "CE", "strike_price": 60300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [60405.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_61100.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2561100CE", "option_type": "CE", "strike_price": 61100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [61205.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_48500.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2548500CE", "option_type": "CE", "strike_price": 48500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [48605.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_51100.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2551100CE", "option_type": "CE", "strike_price": 51100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [51205.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_52400.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2552400CE", "option_type": "CE", "strike_price": 52400.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [52505.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_55000.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2555000CE", "option_type": "CE", "strike_price": 55000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [55105.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_56000.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2556000CE", "option_type": "CE", "strike_price": 56000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [56105.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_56600.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2556600CE", "option_type": "CE", "strike_price": 56600.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [56705.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_57700.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2557700CE", "option_type": "CE", "strike_price": 57700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [57805.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_57900.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2557900CE", "option_type": "CE", "strike_price": 57900.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [58005.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_58500.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2558500CE", "option_type": "CE", "strike_price": 58500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [58605.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_58700.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2558700CE", "option_type": "CE", "strike_price": 58700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [58805.0], "probability_of_profit": 0.**************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_59200.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2559200CE", "option_type": "CE", "strike_price": 59200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [59305.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_59300.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2559300CE", "option_type": "CE", "strike_price": 59300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [59405.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_59500.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2559500CE", "option_type": "CE", "strike_price": 59500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [59605.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_60900.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2560900CE", "option_type": "CE", "strike_price": 60900.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [61005.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_61200.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2561200CE", "option_type": "CE", "strike_price": 61200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [61305.0], "probability_of_profit": 0.***************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_61300.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2561300CE", "option_type": "CE", "strike_price": 61300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [61405.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_61700.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2561700CE", "option_type": "CE", "strike_price": 61700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [61805.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_61900.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2561900CE", "option_type": "CE", "strike_price": 61900.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [62005.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_62300.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2562300CE", "option_type": "CE", "strike_price": 62300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [62405.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_63000.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2563000CE", "option_type": "CE", "strike_price": 63000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [63105.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_47500.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2547500CE", "option_type": "CE", "strike_price": 47500.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [47605.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_50300.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2550300CE", "option_type": "CE", "strike_price": 50300.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [50405.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_51600.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2551600CE", "option_type": "CE", "strike_price": 51600.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [51705.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_52700.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2552700CE", "option_type": "CE", "strike_price": 52700.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [52805.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_53000.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2553000CE", "option_type": "CE", "strike_price": 53000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [53105.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_53800.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2553800CE", "option_type": "CE", "strike_price": 53800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [53905.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_55200.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2555200CE", "option_type": "CE", "strike_price": 55200.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [55305.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_57000.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2557000CE", "option_type": "CE", "strike_price": 57000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [57105.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_59800.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2559800CE", "option_type": "CE", "strike_price": 59800.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [59905.0], "probability_of_profit": 0.****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_60000.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2560000CE", "option_type": "CE", "strike_price": 60000.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [60105.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_60600.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2560600CE", "option_type": "CE", "strike_price": 60600.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [60705.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}, {"strategy_id": "LC_BANKNIFTY_62100.0_20250818114642", "strategy_type": "long_call", "underlying": "BANKNIFTY", "legs": [{"symbol": "BANKNIFTY28AUG2562100CE", "option_type": "CE", "strike_price": 62100.0, "expiry_date": "2024-01-25", "quantity": 1, "premium": 105.0, "underlying": "BANKNIFTY"}], "max_profit": Infinity, "max_loss": 105.0, "break_even_points": [62205.0], "probability_of_profit": 0.*****************, "net_premium": -105.0, "margin_required": 105.0, "risk_reward_ratio": Infinity, "target_profit": 210.0, "stop_loss": 52.5, "created_at": "2025-08-18T11:46:42.613582", "entry_conditions": [{"indicator": "Price", "operator": "break_above", "value": 56863.0}], "exit_conditions": [{"indicator": "Price", "operator": "fall_below", "value": 55737.0}]}]