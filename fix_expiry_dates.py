#!/usr/bin/env python3
"""
Script to fix all hard-coded expiry dates in strategy_generators.py
"""

import re

def fix_expiry_dates():
    file_path = "agents/strategy_generation/strategy_generators.py"
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match hard-coded expiry dates
    patterns_to_fix = [
        (r"expiry_date='2024-01-25'", "expiry_date=lower_call.get('expiry_date', '2024-01-25')"),
        (r"expiry_date='2024-02-29'", "expiry_date=call_data.get('expiry_date', '2024-02-29')"),
    ]
    
    # More specific patterns for different variable contexts
    specific_patterns = [
        # For variables ending with _call, _put, etc.
        (r"(\w+_call)\['symbol'\],\s*option_type='CE',\s*strike_price=(\w+),\s*expiry_date='2024-01-25'", 
         r"\1['symbol'],\n                    option_type='CE',\n                    strike_price=\2,\n                    expiry_date=\1.get('expiry_date', '2024-01-25')"),
        
        (r"(\w+_put)\['symbol'\],\s*option_type='PE',\s*strike_price=(\w+),\s*expiry_date='2024-01-25'", 
         r"\1['symbol'],\n                    option_type='PE',\n                    strike_price=\2,\n                    expiry_date=\1.get('expiry_date', '2024-01-25')"),
        
        # For call_data, put_data patterns
        (r"(call_data)\['symbol'\],\s*option_type='CE',\s*strike_price=(\w+),\s*expiry_date='2024-01-25'", 
         r"\1['symbol'],\n                        option_type='CE',\n                        strike_price=\2,\n                        expiry_date=\1.get('expiry_date', '2024-01-25')"),
        
        (r"(put_data)\['symbol'\],\s*option_type='PE',\s*strike_price=(\w+),\s*expiry_date='2024-01-25'", 
         r"\1['symbol'],\n                        option_type='PE',\n                        strike_price=\2,\n                        expiry_date=\1.get('expiry_date', '2024-01-25')"),
        
        # For monthly expiry
        (r"(call_data)\['symbol'\],\s*option_type='CE',\s*strike_price=(\w+),\s*expiry_date='2024-02-29'", 
         r"\1['symbol'],\n                        option_type='CE',\n                        strike_price=\2,\n                        expiry_date=\1.get('expiry_date', '2024-02-29')"),
        
        (r"(put_data)\['symbol'\],\s*option_type='PE',\s*strike_price=(\w+),\s*expiry_date='2024-02-29'", 
         r"\1['symbol'],\n                        option_type='PE',\n                        strike_price=\2,\n                        expiry_date=\1.get('expiry_date', '2024-02-29')"),
    ]
    
    # Apply specific patterns first
    for pattern, replacement in specific_patterns:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    # Apply general patterns
    for pattern, replacement in patterns_to_fix:
        content = content.replace(pattern, replacement)
    
    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Fixed hard-coded expiry dates in strategy_generators.py")

if __name__ == "__main__":
    fix_expiry_dates()
