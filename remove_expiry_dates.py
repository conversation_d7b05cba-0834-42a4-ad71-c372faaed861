#!/usr/bin/env python3
"""
Script to remove all expiry_date parameters from OptionsLeg constructors
"""

import re

def remove_expiry_dates():
    file_path = "agents/strategy_generation/strategy_generators.py"
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match expiry_date lines in OptionsLeg constructors
    # This will match lines like: expiry_date=something,
    pattern = r'\s*expiry_date=[^,\n]+,\n'
    
    # Remove all expiry_date lines
    content = re.sub(pattern, '\n', content)
    
    # Also remove any remaining expiry_date parameters without trailing comma
    pattern2 = r'\s*expiry_date=[^,\n)]+\n'
    content = re.sub(pattern2, '\n', content)
    
    # Clean up any double newlines that might have been created
    content = re.sub(r'\n\n\n+', '\n\n', content)
    
    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Removed all expiry_date parameters from OptionsLeg constructors")

if __name__ == "__main__":
    remove_expiry_dates()
